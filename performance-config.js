// 性能优化配置文件
const PerformanceConfig = {
    // 文件处理配置
    file: {
        maxSize: 50 * 1024 * 1024, // 增加到50MB
        chunkSize: 1024 * 1024,    // 1MB块大小
        batchSize: 1000,           // 批处理大小
        progressUpdateInterval: 100 // 进度更新间隔
    },
    
    // 内存管理配置
    memory: {
        enableGarbageCollection: true,
        gcInterval: 5000,          // 5秒清理一次
        maxCacheSize: 100 * 1024 * 1024 // 100MB缓存限制
    },
    
    // UI更新配置
    ui: {
        debounceDelay: 300,        // 防抖延迟
        throttleDelay: 100,        // 节流延迟
        virtualScrollThreshold: 1000 // 虚拟滚动阈值
    },
    
    // 异步处理配置
    async: {
        useWebWorkers: false,      // 暂时禁用Web Workers
        maxConcurrency: 4,         // 最大并发数
        timeSlice: 16             // 时间片（毫秒）
    }
};

// 性能监控工具
class PerformanceMonitor {
    constructor() {
        this.metrics = {
            fileLoadTime: 0,
            dataProcessTime: 0,
            renderTime: 0,
            memoryUsage: 0
        };
        this.startTimes = new Map();
    }
    
    start(operation) {
        this.startTimes.set(operation, performance.now());
    }
    
    end(operation) {
        const startTime = this.startTimes.get(operation);
        if (startTime) {
            const duration = performance.now() - startTime;
            this.metrics[operation] = duration;
            console.log(`${operation}: ${duration.toFixed(2)}ms`);
            this.startTimes.delete(operation);
            return duration;
        }
        return 0;
    }
    
    getMemoryUsage() {
        if (performance.memory) {
            return {
                used: performance.memory.usedJSHeapSize,
                total: performance.memory.totalJSHeapSize,
                limit: performance.memory.jsHeapSizeLimit
            };
        }
        return null;
    }
    
    logMetrics() {
        console.table(this.metrics);
        const memory = this.getMemoryUsage();
        if (memory) {
            console.log('内存使用:', {
                used: `${(memory.used / 1024 / 1024).toFixed(2)}MB`,
                total: `${(memory.total / 1024 / 1024).toFixed(2)}MB`,
                limit: `${(memory.limit / 1024 / 1024).toFixed(2)}MB`
            });
        }
    }
}

// 内存管理工具
class MemoryManager {
    constructor() {
        this.cache = new Map();
        this.maxCacheSize = PerformanceConfig.memory.maxCacheSize;
    }
    
    set(key, value) {
        // 检查缓存大小
        if (this.getCacheSize() > this.maxCacheSize) {
            this.clearOldestEntries();
        }
        this.cache.set(key, {
            value,
            timestamp: Date.now()
        });
    }
    
    get(key) {
        const item = this.cache.get(key);
        return item ? item.value : null;
    }
    
    getCacheSize() {
        return JSON.stringify([...this.cache.values()]).length;
    }
    
    clearOldestEntries() {
        const entries = [...this.cache.entries()];
        entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
        
        // 删除最旧的50%条目
        const deleteCount = Math.floor(entries.length * 0.5);
        for (let i = 0; i < deleteCount; i++) {
            this.cache.delete(entries[i][0]);
        }
    }
    
    clear() {
        this.cache.clear();
        if (window.gc) {
            window.gc(); // 强制垃圾回收（仅在开发环境）
        }
    }
}

// 异步任务队列
class TaskQueue {
    constructor(maxConcurrency = 4) {
        this.queue = [];
        this.running = [];
        this.maxConcurrency = maxConcurrency;
    }
    
    async add(task) {
        return new Promise((resolve, reject) => {
            this.queue.push({
                task,
                resolve,
                reject
            });
            this.process();
        });
    }
    
    async process() {
        if (this.running.length >= this.maxConcurrency || this.queue.length === 0) {
            return;
        }
        
        const { task, resolve, reject } = this.queue.shift();
        const promise = this.runTask(task);
        this.running.push(promise);
        
        try {
            const result = await promise;
            resolve(result);
        } catch (error) {
            reject(error);
        } finally {
            this.running = this.running.filter(p => p !== promise);
            this.process(); // 处理下一个任务
        }
    }
    
    async runTask(task) {
        return new Promise((resolve, reject) => {
            // 使用 setTimeout 确保任务异步执行
            setTimeout(async () => {
                try {
                    const result = await task();
                    resolve(result);
                } catch (error) {
                    reject(error);
                }
            }, 0);
        });
    }
}

// 防抖函数
function debounce(func, delay) {
    let timeoutId;
    return function (...args) {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => func.apply(this, args), delay);
    };
}

// 节流函数
function throttle(func, delay) {
    let lastCall = 0;
    return function (...args) {
        const now = Date.now();
        if (now - lastCall >= delay) {
            lastCall = now;
            return func.apply(this, args);
        }
    };
}

// 导出配置和工具
window.PerformanceConfig = PerformanceConfig;
window.PerformanceMonitor = PerformanceMonitor;
window.MemoryManager = MemoryManager;
window.TaskQueue = TaskQueue;
window.debounce = debounce;
window.throttle = throttle;
