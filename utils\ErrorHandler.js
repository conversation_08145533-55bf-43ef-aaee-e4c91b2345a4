/**
 * 统一错误处理模块
 * 提供统一的错误处理、日志记录和用户提示功能
 */

import { ERROR_MESSAGES, SUCCESS_MESSAGES, INFO_MESSAGES, DOM_IDS } from './constants.js';

/**
 * 错误处理器类
 */
export class ErrorHandler {
    /**
     * 处理错误
     * @param {Error|string} error - 错误对象或错误消息
     * @param {string} context - 错误上下文
     * @param {boolean} showToUser - 是否向用户显示错误
     */
    static handle(error, context = '操作', showToUser = true) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        const fullMessage = `${context}: ${errorMessage}`;
        
        // 记录到控制台
        console.error(fullMessage, error instanceof Error ? error.stack : '');
        
        // 向用户显示错误
        if (showToUser) {
            this.showMessage(fullMessage, 'error');
        }
        
        // 可以在这里添加错误上报逻辑
        this.reportError(error, context);
        
        return false;
    }
    
    /**
     * 处理文件相关错误
     * @param {Error|string} error - 错误对象或错误消息
     * @param {string} fileName - 文件名
     */
    static handleFileError(error, fileName = '') {
        const context = fileName ? `文件处理 (${fileName})` : '文件处理';
        return this.handle(error, context);
    }
    
    /**
     * 处理Excel相关错误
     * @param {Error|string} error - 错误对象或错误消息
     * @param {string} operation - 操作类型
     */
    static handleExcelError(error, operation = 'Excel操作') {
        return this.handle(error, operation);
    }
    
    /**
     * 处理网络相关错误
     * @param {Error|string} error - 错误对象或错误消息
     * @param {string} url - 请求URL
     */
    static handleNetworkError(error, url = '') {
        const context = url ? `网络请求 (${url})` : '网络请求';
        return this.handle(error, context);
    }
    
    /**
     * 验证文件
     * @param {File} file - 文件对象
     * @throws {Error} 验证失败时抛出错误
     */
    static validateFile(file) {
        console.log('validateFile called with:', file);
        console.log('File name:', file?.name);
        console.log('File size:', file?.size);

        if (!file) {
            throw new Error('请选择文件');
        }

        if (!file.name || !file.name.toLowerCase().endsWith('.xlsx')) {
            throw new Error(ERROR_MESSAGES.FILE_TYPE_ERROR);
        }

        if (file.size > 10 * 1024 * 1024) {
            throw new Error(ERROR_MESSAGES.FILE_SIZE_ERROR);
        }
        
        return true;
    }
    
    /**
     * 验证数据
     * @param {any} data - 要验证的数据
     * @param {string} dataType - 数据类型描述
     * @throws {Error} 验证失败时抛出错误
     */
    static validateData(data, dataType = '数据') {
        if (!data) {
            throw new Error(`${dataType}不能为空`);
        }
        
        if (Array.isArray(data) && data.length === 0) {
            throw new Error(`${dataType}不能为空数组`);
        }
        
        return true;
    }
    
    /**
     * 安全执行异步操作
     * @param {Function} asyncFn - 异步函数
     * @param {string} context - 操作上下文
     * @param {boolean} showProgress - 是否显示进度
     * @returns {Promise<any>} 执行结果
     */
    static async safeExecute(asyncFn, context = '操作', showProgress = false) {
        try {
            if (showProgress) {
                this.showMessage(`正在${context}...`, 'info');
            }
            
            const result = await asyncFn();
            
            if (showProgress) {
                this.showMessage(`${context}完成`, 'success');
            }
            
            return result;
        } catch (error) {
            this.handle(error, context);
            throw error;
        }
    }
    
    /**
     * 安全执行同步操作
     * @param {Function} syncFn - 同步函数
     * @param {string} context - 操作上下文
     * @returns {any} 执行结果
     */
    static safeExecuteSync(syncFn, context = '操作') {
        try {
            return syncFn();
        } catch (error) {
            this.handle(error, context);
            throw error;
        }
    }
    
    /**
     * 显示消息给用户
     * @param {string} text - 消息文本
     * @param {string} type - 消息类型 ('success', 'error', 'info')
     */
    static showMessage(text, type = 'info') {
        const messageArea = document.getElementById(DOM_IDS.MESSAGE_AREA);
        if (!messageArea) {
            console.warn('消息显示区域未找到');
            return;
        }
        
        messageArea.textContent = text;
        messageArea.className = `message ${type}`;
        messageArea.style.display = 'block';
        
        // 成功消息自动隐藏
        if (type === 'success') {
            setTimeout(() => {
                messageArea.style.display = 'none';
                this.hideProgress();
            }, 3000);
        }
    }
    
    /**
     * 显示进度条
     */
    static showProgress() {
        const progressContainer = document.getElementById(DOM_IDS.PROGRESS_CONTAINER);
        if (progressContainer) {
            progressContainer.style.display = 'block';
        }
    }
    
    /**
     * 隐藏进度条
     */
    static hideProgress() {
        const progressContainer = document.getElementById(DOM_IDS.PROGRESS_CONTAINER);
        if (progressContainer) {
            progressContainer.style.display = 'none';
        }
    }
    
    /**
     * 更新进度条
     * @param {number} percentage - 进度百分比
     * @param {string} text - 进度文本
     */
    static updateProgress(percentage, text = '') {
        const progressBar = document.getElementById(DOM_IDS.PROGRESS_BAR);
        const progressText = document.getElementById(DOM_IDS.PROGRESS_TEXT);
        
        if (progressBar) {
            progressBar.style.width = percentage + '%';
        }
        
        if (progressText) {
            progressText.textContent = text || `${percentage}%`;
        }
    }
    
    /**
     * 上报错误（可扩展）
     * @param {Error|string} error - 错误对象或错误消息
     * @param {string} context - 错误上下文
     */
    static reportError(error, context) {
        // 这里可以添加错误上报逻辑
        // 例如发送到错误监控服务
        const errorData = {
            message: error instanceof Error ? error.message : String(error),
            stack: error instanceof Error ? error.stack : '',
            context,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href
        };
        
        // 暂时只记录到控制台，可以扩展为发送到服务器
        console.log('Error Report:', errorData);
    }
    
    /**
     * 创建自定义错误
     * @param {string} message - 错误消息
     * @param {string} code - 错误代码
     * @param {any} details - 错误详情
     * @returns {Error} 自定义错误对象
     */
    static createError(message, code = 'UNKNOWN_ERROR', details = null) {
        const error = new Error(message);
        error.code = code;
        error.details = details;
        return error;
    }
    
    /**
     * 检查是否为特定类型的错误
     * @param {Error} error - 错误对象
     * @param {string} code - 错误代码
     * @returns {boolean} 是否匹配
     */
    static isErrorType(error, code) {
        return error && error.code === code;
    }
}

/**
 * 自定义错误类
 */
export class FileProcessingError extends Error {
    constructor(message, fileName = '', originalError = null) {
        super(message);
        this.name = 'FileProcessingError';
        this.fileName = fileName;
        this.originalError = originalError;
    }
}

export class ExcelProcessingError extends Error {
    constructor(message, operation = '', originalError = null) {
        super(message);
        this.name = 'ExcelProcessingError';
        this.operation = operation;
        this.originalError = originalError;
    }
}

export class ValidationError extends Error {
    constructor(message, field = '', value = null) {
        super(message);
        this.name = 'ValidationError';
        this.field = field;
        this.value = value;
    }
}

// 导出默认错误处理器
export default ErrorHandler;
