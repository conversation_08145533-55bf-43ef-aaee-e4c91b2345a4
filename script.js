/**
 * Excel转换器主入口文件
 * 负责初始化应用和启动主程序
 */

// 导入主应用类
import ExcelConverter from "./modules/ExcelConverter.js";

/**
 * 应用初始化
 */
async function initializeApp() {
    try {
        console.log("正在初始化Excel转换器...");
        
        // 初始化主应用
        await ExcelConverter.initialize();
        
        console.log("Excel转换器初始化完成");
        
    } catch (error) {
        console.error("应用初始化失败:", error);
        
        // 显示错误信息给用户
        const messageArea = document.getElementById("messageArea");
        if (messageArea) {
            messageArea.innerHTML = `
                <div class="error-message">
                    <h3>应用初始化失败</h3>
                    <p>错误信息: ${error.message}</p>
                    <p>请刷新页面重试，如果问题持续存在，请检查浏览器控制台获取更多信息。</p>
                </div>
            `;
        }
    }
}

/**
 * DOM加载完成后初始化应用
 */
document.addEventListener("DOMContentLoaded", initializeApp);

/**
 * 页面卸载时清理资源
 */
window.addEventListener("beforeunload", () => {
    try {
        ExcelConverter.destroy();
    } catch (error) {
        console.warn("清理资源时出错:", error);
    }
});

/**
 * 全局错误处理
 */
window.addEventListener("error", (event) => {
    console.error("全局错误:", event.error);
});

window.addEventListener("unhandledrejection", (event) => {
    console.error("未处理的Promise拒绝:", event.reason);
    event.preventDefault();
});

// 导出应用实例供调试使用
window.ExcelConverter = ExcelConverter;
