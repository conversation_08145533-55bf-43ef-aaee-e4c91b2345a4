/**
 * Excel处理模块
 * 统一Excel读取、写入和格式化功能
 */

import { EXCEL_CONFIG, FILE_CONFIG, INFO_MESSAGES } from '../utils/constants.js';
import ErrorHandler, { ExcelProcessingError } from '../utils/ErrorHandler.js';
import DateUtils from './DateUtils.js';

/**
 * Excel处理器类
 */
export class ExcelHandler {
    constructor() {
        this.onProgress = null;
    }
    
    /**
     * 设置进度回调
     * @param {Function} callback - 进度回调函数
     */
    setProgressCallback(callback) {
        this.onProgress = callback;
    }
    
    /**
     * 读取Excel文件
     * @param {ArrayBuffer} arrayBuffer - 文件内容
     * @param {string} fileName - 文件名
     * @returns {Promise<Object>} 工作簿对象
     */
    async readExcelFile(arrayBuffer, fileName = '') {
        try {
            ErrorHandler.showMessage(INFO_MESSAGES.PARSING_EXCEL, 'info');
            
            const workbook = new ExcelJS.Workbook();
            await workbook.xlsx.load(arrayBuffer);
            
            return workbook;
        } catch (error) {
            throw new ExcelProcessingError('Excel文件解析失败', 'read', error);
        }
    }
    
    /**
     * 使用Web Worker读取Excel文件
     * @param {ArrayBuffer} arrayBuffer - 文件内容
     * @param {string} fileName - 文件名
     * @returns {Promise<Object>} 处理后的数据
     */
    async readExcelFileWithWorker(arrayBuffer, fileName = '') {
        return new Promise((resolve, reject) => {
            const worker = new Worker('excel-worker.js');
            
            // 监听Worker消息
            worker.onmessage = (e) => {
                const { type, progress, message, data, error } = e.data;
                
                switch (type) {
                    case 'PROGRESS':
                        ErrorHandler.showMessage(message, 'info');
                        break;
                    case 'SUCCESS':
                        worker.terminate();
                        resolve(data);
                        break;
                    case 'ERROR':
                        worker.terminate();
                        reject(new ExcelProcessingError(error, 'worker'));
                        break;
                }
            };
            
            worker.onerror = (error) => {
                worker.terminate();
                reject(new ExcelProcessingError('Worker处理失败: ' + error.message, 'worker'));
            };
            
            // 发送数据给Worker处理
            worker.postMessage({
                type: 'PROCESS_EXCEL_FILE',
                data: {
                    arrayBuffer,
                    fileName
                }
            });
        });
    }
    
    /**
     * 处理工作簿数据
     * @param {Object} workbook - ExcelJS工作簿对象
     * @returns {Promise<Object>} 处理后的数据
     */
    async processWorkbookData(workbook) {
        return new Promise((resolve, reject) => {
            try {
                // 获取第一个工作表
                const worksheet = workbook.worksheets[0];
                if (!worksheet) {
                    throw new ExcelProcessingError('工作簿中没有找到工作表', 'process');
                }
                
                // 提取数据
                const data = this.extractDataFromWorksheet(worksheet);
                
                // 异步处理数据行
                setTimeout(() => {
                    try {
                        const result = this.processDataRows(data);
                        resolve(result);
                    } catch (error) {
                        reject(new ExcelProcessingError('数据处理失败', 'process', error));
                    }
                }, 0);
                
            } catch (error) {
                reject(new ExcelProcessingError('工作簿处理失败', 'process', error));
            }
        });
    }
    
    /**
     * 从工作表提取数据
     * @param {Object} worksheet - ExcelJS工作表对象
     * @returns {Array} 提取的数据
     */
    extractDataFromWorksheet(worksheet) {
        const data = [];
        const maxColumns = worksheet.columnCount || 10;
        const batchSize = FILE_CONFIG.BATCH_SIZE;
        let currentBatch = [];
        
        worksheet.eachRow((row, rowNumber) => {
            const rowData = new Array(maxColumns);
            
            // 只遍历实际有数据的列
            row.eachCell({ includeEmpty: false }, (cell, colNumber) => {
                if (colNumber <= maxColumns) {
                    rowData[colNumber - 1] = cell.value;
                }
            });
            
            currentBatch.push(rowData);
            
            // 批量处理以减少内存压力
            if (currentBatch.length >= batchSize) {
                data.push(...currentBatch);
                currentBatch = [];
            }
        });
        
        // 处理剩余的行
        if (currentBatch.length > 0) {
            data.push(...currentBatch);
        }
        
        if (data.length === 0) {
            throw new ExcelProcessingError('工作表中没有数据', 'extract');
        }
        
        return data;
    }
    
    /**
     * 处理数据行
     * @param {Array} data - 原始数据
     * @returns {Object} 处理后的数据 {headers, rows, mergeInfo}
     */
    processDataRows(data) {
        // 智能检测表头位置
        const { headers, dataStartRow } = this.detectHeaders(data);
        
        // 处理列标题
        const { headers: processedHeaders, columnIndexMap } = this.processHeaders(headers);

        // 处理数据行
        const dataRows = data.slice(dataStartRow).filter(row =>
            row.some(cell => cell !== null && cell !== undefined && String(cell).trim() !== '')
        );

        if (dataRows.length === 0) {
            throw new ExcelProcessingError('没有找到有效的数据行', 'process');
        }

        // 找到日期列的索引
        const dateColumnIndex = DateUtils.findDateColumnIndex(processedHeaders);
        if (dateColumnIndex === -1) {
            throw new ExcelProcessingError('未找到日期列', 'process');
        }

        // 转换数据行
        const processedRows = this.transformDataRows(dataRows, processedHeaders, dateColumnIndex, columnIndexMap);

        // 按日期分组数据
        const groupedData = DateUtils.groupDataByDate(processedRows, dateColumnIndex, processedHeaders);
        
        return {
            headers: processedHeaders,
            rows: groupedData.rows,
            mergeInfo: groupedData.mergeInfo
        };
    }
    
    /**
     * 检测表头位置
     * @param {Array} data - 原始数据
     * @returns {Object} {headers, dataStartRow}
     */
    detectHeaders(data) {
        let headers = null;
        let dataStartRow = 1;
        
        // 检查第一行是否是合并的标题行
        if (data[0]) {
            const firstRowNonEmptyCells = data[0].filter(cell =>
                cell !== null && cell !== undefined && String(cell).trim() !== ''
            );
            
            if (firstRowNonEmptyCells.length > 0) {
                const firstCellValue = String(firstRowNonEmptyCells[0]).trim();
                const allSame = firstRowNonEmptyCells.every(cell =>
                    String(cell).trim() === firstCellValue
                );
                
                if (allSame && firstCellValue.includes('科陆流水线')) {
                    // 第一行是标题行，第二行是列标题
                    headers = data[1];
                    dataStartRow = 2;
                } else {
                    // 第一行就是列标题
                    headers = data[0];
                    dataStartRow = 1;
                }
            } else {
                throw new ExcelProcessingError('第一行没有有效数据', 'detect');
            }
        } else {
            throw new ExcelProcessingError('数据为空', 'detect');
        }
        
        if (!headers || headers.length === 0) {
            throw new ExcelProcessingError('无法读取表头信息', 'detect');
        }
        
        return { headers, dataStartRow };
    }
    
    /**
     * 处理表头
     * @param {Array} headers - 原始表头
     * @returns {Object} {headers: 处理后的表头, columnIndexMap: 列索引映射}
     */
    processHeaders(headers) {
        // 清理和过滤列标题
        const cleanHeaders = headers.map(header => String(header || '').trim()).filter(h => h);

        if (cleanHeaders.length === 0) {
            throw new ExcelProcessingError('未找到有效的列标题', 'process');
        }

        // 过滤掉排除的列
        const filteredHeaders = [];
        const columnIndexMap = new Map();

        // 找到关键列的索引
        let dateColumnIndex = -1;
        let failureColumnIndex = -1;

        cleanHeaders.forEach((header, index) => {
            if (!EXCEL_CONFIG.EXCLUDED_COLUMNS.includes(header)) {
                const headerStr = String(header).toLowerCase();

                // 检查是否是日期列
                if (headerStr.includes('日期') || headerStr.includes('date') || headerStr.includes('时间')) {
                    dateColumnIndex = filteredHeaders.length;
                }

                // 检查是否是故障处理情况列
                if (headerStr.includes('故障')) {
                    failureColumnIndex = filteredHeaders.length;
                }

                columnIndexMap.set(filteredHeaders.length, index);
                filteredHeaders.push(header);
            }
        });

        // 插入新列并更新映射
        const updatedMapping = this.insertNewColumns(filteredHeaders, columnIndexMap, dateColumnIndex, failureColumnIndex);

        return { headers: filteredHeaders, columnIndexMap: updatedMapping };
    }
    
    /**
     * 插入新列并更新列索引映射
     * @param {Array} headers - 表头数组
     * @param {Map} columnIndexMap - 原始列索引映射
     * @param {number} dateColumnIndex - 日期列索引
     * @param {number} failureColumnIndex - 故障列索引
     * @returns {Map} 更新后的列索引映射
     */
    insertNewColumns(headers, columnIndexMap, dateColumnIndex, failureColumnIndex) {
        // 确定维护保养情况列的插入位置
        let maintenanceInsertIndex = headers.length;

        if (dateColumnIndex !== -1 && failureColumnIndex !== -1) {
            maintenanceInsertIndex = Math.max(dateColumnIndex + 1, failureColumnIndex);
        } else if (dateColumnIndex !== -1) {
            maintenanceInsertIndex = dateColumnIndex + 1;
        }

        // 插入维护保养情况列
        headers.splice(maintenanceInsertIndex, 0, EXCEL_CONFIG.NEW_COLUMNS.MAINTENANCE);

        // 添加备注列到末尾
        headers.push(EXCEL_CONFIG.NEW_COLUMNS.REMARK);

        // 更新列索引映射
        const newColumnIndexMap = new Map();
        let filteredIndex = 0;

        headers.forEach((header, index) => {
            if (header === EXCEL_CONFIG.NEW_COLUMNS.MAINTENANCE) {
                newColumnIndexMap.set(index, -1); // -1表示新添加的维护保养情况列
            } else if (header === EXCEL_CONFIG.NEW_COLUMNS.REMARK) {
                newColumnIndexMap.set(index, -2); // -2表示新添加的备注列
            } else {
                newColumnIndexMap.set(index, columnIndexMap.get(filteredIndex));
                filteredIndex++;
            }
        });

        return newColumnIndexMap;
    }
    
    /**
     * 转换数据行
     * @param {Array} dataRows - 原始数据行
     * @param {Array} headers - 处理后的表头
     * @param {number} dateColumnIndex - 日期列索引
     * @param {Map} columnIndexMap - 列索引映射
     * @returns {Array} 转换后的数据行
     */
    transformDataRows(dataRows, headers, dateColumnIndex, columnIndexMap) {
        return dataRows.map(row => {
            const newRow = [];

            headers.forEach((header, newColIndex) => {
                let cellValue = '';

                if (header === EXCEL_CONFIG.NEW_COLUMNS.MAINTENANCE) {
                    cellValue = ''; // 维护保养情况列为空
                } else if (header === EXCEL_CONFIG.NEW_COLUMNS.REMARK) {
                    cellValue = EXCEL_CONFIG.DEFAULT_REMARK; // 备注列默认值
                } else {
                    // 获取原始列索引
                    const originalColIndex = columnIndexMap.get(newColIndex);
                    if (originalColIndex !== undefined && originalColIndex >= 0) {
                        cellValue = row[originalColIndex] || '';

                        // 如果是日期列，格式化日期
                        if (newColIndex === dateColumnIndex && cellValue) {
                            cellValue = DateUtils.formatDateValue(cellValue);
                        }
                    }
                }

                newRow.push(cellValue);
            });

            return newRow;
        });
    }
    
    /**
     * 创建格式化的工作簿
     * @param {Object} data - 处理后的数据
     * @returns {Promise<Object>} ExcelJS工作簿对象
     */
    async createFormattedWorkbook(data) {
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet(EXCEL_CONFIG.WORKSHEET_NAME);
        
        // 添加主标题行
        const titleRow = worksheet.addRow([EXCEL_CONFIG.MAIN_TITLE]);
        worksheet.mergeCells(1, 1, 1, data.headers.length);
        
        // 设置主标题样式
        const titleCell = worksheet.getCell(1, 1);
        Object.assign(titleCell, EXCEL_CONFIG.STYLES.MAIN_TITLE);
        
        // 添加列标题行
        const headerRow = worksheet.addRow(data.headers);
        this.applyHeaderStyles(headerRow);
        
        // 添加数据行
        data.rows.forEach(rowData => {
            const dataRow = worksheet.addRow(rowData);
            this.applyDataRowStyles(dataRow, data.headers);
        });
        
        // 设置列宽和行高
        this.applyWorksheetFormatting(worksheet, data);
        
        // 应用单元格合并
        this.applyMerges(worksheet, data.mergeInfo);
        
        return workbook;
    }
    
    /**
     * 应用表头样式
     * @param {Object} headerRow - 表头行对象
     */
    applyHeaderStyles(headerRow) {
        headerRow.eachCell((cell) => {
            Object.assign(cell, EXCEL_CONFIG.STYLES.COLUMN_HEADER);
            cell.border = this.createBorder();
        });
    }
    
    /**
     * 应用数据行样式
     * @param {Object} dataRow - 数据行对象
     * @param {Array} headers - 表头数组
     */
    applyDataRowStyles(dataRow, headers) {
        dataRow.eachCell((cell, colNumber) => {
            const columnHeader = headers[colNumber - 1];
            
            if (columnHeader && columnHeader.includes('故障处理情况')) {
                Object.assign(cell, EXCEL_CONFIG.STYLES.DATA_CELL_LEFT);
            } else {
                Object.assign(cell, EXCEL_CONFIG.STYLES.DATA_CELL);
            }
            
            cell.border = this.createBorder();
            
            // 处理日期格式
            if (columnHeader && columnHeader.includes('日期') && cell.value instanceof Date) {
                cell.numFmt = EXCEL_CONFIG.DATE_FORMAT;
            }
        });
    }
    
    /**
     * 创建边框样式
     * @returns {Object} 边框样式对象
     */
    createBorder() {
        return {
            top: { style: 'thin', color: { argb: 'FF000000' } },
            left: { style: 'thin', color: { argb: 'FF000000' } },
            bottom: { style: 'thin', color: { argb: 'FF000000' } },
            right: { style: 'thin', color: { argb: 'FF000000' } }
        };
    }
    
    /**
     * 应用工作表格式化
     * @param {Object} worksheet - 工作表对象
     * @param {Object} data - 数据对象
     */
    applyWorksheetFormatting(worksheet, data) {
        // 设置列宽
        data.headers.forEach((header, index) => {
            const width = this.getColumnWidth(header);
            worksheet.getColumn(index + 1).width = width;
        });
        
        // 设置行高
        worksheet.eachRow((row) => {
            row.height = 25;
        });
    }
    
    /**
     * 获取列宽
     * @param {string} header - 列标题
     * @returns {number} 列宽
     */
    getColumnWidth(header) {
        for (const [keyword, width] of Object.entries(EXCEL_CONFIG.COLUMN_WIDTHS)) {
            if (header.includes(keyword)) {
                return width;
            }
        }
        return EXCEL_CONFIG.COLUMN_WIDTHS.default;
    }
    
    /**
     * 应用单元格合并
     * @param {Object} worksheet - 工作表对象
     * @param {Array} mergeInfo - 合并信息
     */
    applyMerges(worksheet, mergeInfo) {
        if (mergeInfo && mergeInfo.length > 0) {
            mergeInfo.forEach((merge) => {
                const startRow = merge.startRow + 3; // +2 因为有主标题和列标题，+1 因为ExcelJS从1开始
                const endRow = merge.endRow + 3;
                const startCol = merge.startCol + 1;
                const endCol = merge.endCol + 1;
                
                try {
                    worksheet.mergeCells(startRow, startCol, endRow, endCol);
                } catch (error) {
                    console.warn('合并单元格失败:', error, merge);
                }
            });
        }
    }
    
    /**
     * 下载工作簿
     * @param {Object} workbook - ExcelJS工作簿对象
     * @returns {Promise<string>} 文件名
     */
    async downloadWorkbook(workbook) {
        try {
            // 生成文件名
            const today = new Date();
            const dateStr = today.getFullYear() +
                           String(today.getMonth() + 1).padStart(2, '0') +
                           String(today.getDate()).padStart(2, '0');
            const fileName = `${EXCEL_CONFIG.OUTPUT_FILE_PREFIX}${dateStr}${EXCEL_CONFIG.OUTPUT_FILE_EXTENSION}`;
            
            // 生成文件buffer
            const buffer = await workbook.xlsx.writeBuffer();
            
            // 创建Blob并下载
            const blob = new Blob([buffer], {
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            });
            
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = fileName;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
            
            console.log(`文件下载成功: ${fileName}`);
            return fileName;
        } catch (error) {
            throw new ExcelProcessingError('文件下载失败', 'download', error);
        }
    }
}

export default new ExcelHandler();
