/**
 * 日期处理工具模块
 * 提供日期格式化、转换、分组等功能
 */

import { DATE_CONFIG } from '../utils/constants.js';

/**
 * 日期工具类
 */
export class DateUtils {
    /**
     * 查找日期列的索引
     * @param {Array} headers - 表头数组
     * @returns {number} 日期列索引，未找到返回-1
     */
    static findDateColumnIndex(headers) {
        const dateKeywords = DATE_CONFIG.DATE_KEYWORDS;
        
        for (let i = 0; i < headers.length; i++) {
            const header = String(headers[i]).toLowerCase();
            if (dateKeywords.some(keyword => header.includes(keyword))) {
                return i;
            }
        }
        
        return -1;
    }
    
    /**
     * 格式化日期值
     * @param {any} dateValue - 日期值（可能是Excel序列号、字符串或Date对象）
     * @returns {Date|string|null} 格式化后的日期
     */
    static formatDateValue(dateValue) {
        if (!dateValue) return null;
        
        console.log('formatDateValue input:', dateValue, 'type:', typeof dateValue);
        
        // 如果是Excel日期序列号
        if (typeof dateValue === 'number' && dateValue > 1) {
            try {
                // 使用SheetJS的日期转换函数
                if (typeof XLSX !== 'undefined' && XLSX.SSF && XLSX.SSF.parse_date_code) {
                    const jsDate = XLSX.SSF.parse_date_code(dateValue);
                    if (jsDate && jsDate.y && jsDate.m && jsDate.d) {
                        const date = new Date(jsDate.y, jsDate.m - 1, jsDate.d, 0, 0, 0, 0);
                        console.log('Excel serial number converted:', dateValue, '->', date);
                        return date;
                    }
                }
            } catch (error) {
                console.warn('Excel date conversion failed, trying manual conversion:', error);
            }
            
            // 备用转换方法：手动Excel日期转换
            try {
                const convertedDate = this.convertExcelSerialDate(dateValue);
                console.log('Manual Excel date conversion:', dateValue, '->', convertedDate);
                return convertedDate;
            } catch (error) {
                console.warn('Manual date conversion also failed:', error);
            }
        }
        
        // 如果是字符串，尝试解析为日期
        if (typeof dateValue === 'string') {
            try {
                const date = new Date(dateValue);
                if (!isNaN(date.getTime())) {
                    const finalDate = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0);
                    console.log('String date converted:', dateValue, '->', finalDate);
                    return finalDate;
                }
            } catch (error) {
                console.warn('String date parsing failed:', error);
            }
            // 如果无法解析为日期，返回原字符串
            return dateValue;
        }
        
        // 如果已经是Date对象，确保只包含日期部分
        if (dateValue instanceof Date) {
            const finalDate = new Date(dateValue.getFullYear(), dateValue.getMonth(), dateValue.getDate(), 0, 0, 0, 0);
            console.log('Date object normalized:', dateValue, '->', finalDate);
            return finalDate;
        }
        
        console.warn('Unable to convert date value:', dateValue);
        return String(dateValue);
    }
    
    /**
     * 转换Excel序列号为日期
     * @param {number} serialDate - Excel日期序列号
     * @returns {Date} 转换后的日期
     */
    static convertExcelSerialDate(serialDate) {
        // Excel日期系统：1900年1月1日 = 1，但Excel错误地认为1900年是闰年
        let excelDate = serialDate;
        
        // 处理Excel的1900年闰年bug：如果日期 >= 60（1900年3月1日），需要减1
        if (excelDate >= DATE_CONFIG.EXCEL_DATE_SYSTEM.LEAP_YEAR_BUG_THRESHOLD) {
            excelDate = excelDate - 1;
        }
        
        // 计算从1900年1月1日开始的天数
        const baseDate = DATE_CONFIG.EXCEL_DATE_SYSTEM.BASE_DATE;
        const resultDate = new Date(baseDate.getTime() + (excelDate - 1) * 86400 * 1000);
        
        // 确保只包含日期部分
        return new Date(resultDate.getFullYear(), resultDate.getMonth(), resultDate.getDate(), 0, 0, 0, 0);
    }
    
    /**
     * 获取日期的分组键（用于分组）
     * @param {any} dateValue - 日期值
     * @returns {string} 日期分组键
     */
    static getDateGroupKey(dateValue) {
        const formattedDate = this.formatDateValue(dateValue);
        
        if (formattedDate instanceof Date) {
            // 使用本地时间而不是UTC时间，避免时区偏差
            const year = formattedDate.getFullYear();
            const month = String(formattedDate.getMonth() + 1).padStart(2, '0');
            const day = String(formattedDate.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }
        
        return String(formattedDate || '');
    }
    
    /**
     * 按日期分组数据
     * @param {Array} rows - 数据行
     * @param {number} dateColumnIndex - 日期列索引
     * @param {Array} headers - 表头
     * @returns {Object} 分组结果 {rows, mergeInfo}
     */
    static groupDataByDate(rows, dateColumnIndex, headers) {
        if (rows.length === 0) {
            return { rows: [], mergeInfo: [] };
        }
        
        // 按日期分组
        const dateGroups = new Map();
        
        rows.forEach((row, index) => {
            const dateGroupKey = this.getDateGroupKey(row[dateColumnIndex]);
            
            if (!dateGroups.has(dateGroupKey)) {
                dateGroups.set(dateGroupKey, []);
            }
            dateGroups.get(dateGroupKey).push({ row, originalIndex: index });
        });
        
        // 处理分组数据并应用合并逻辑
        const processedRows = [];
        const mergeInfo = [];
        
        // 找到关键列的索引
        const maintenanceColumnIndex = headers.indexOf('维护保养情况');
        
        dateGroups.forEach((group) => {
            const groupStartRow = processedRows.length;
            
            group.forEach((item, groupIndex) => {
                const row = [...item.row];
                
                // 处理维护保养情况列合并：只在第一行显示
                if (groupIndex > 0 && maintenanceColumnIndex !== -1) {
                    row[maintenanceColumnIndex] = '';
                }
                processedRows.push(row);
            });
            
            // 记录合并信息
            if (group.length > 1) {
                // 日期列合并
                mergeInfo.push({
                    startRow: groupStartRow,
                    endRow: groupStartRow + group.length - 1,
                    startCol: dateColumnIndex,
                    endCol: dateColumnIndex,
                    type: 'date'
                });
                
                // 维护保养情况列合并
                if (maintenanceColumnIndex !== -1) {
                    mergeInfo.push({
                        startRow: groupStartRow,
                        endRow: groupStartRow + group.length - 1,
                        startCol: maintenanceColumnIndex,
                        endCol: maintenanceColumnIndex,
                        type: 'maintenance'
                    });
                }
            }
        });
        
        return { rows: processedRows, mergeInfo };
    }
    
    /**
     * 分析日期分布
     * @param {Object} processedData - 处理后的数据
     * @returns {Object} 日期分布信息
     */
    static analyzeDateDistribution(processedData) {
        if (!processedData || !processedData.headers || !processedData.rows) {
            console.warn('无法分析日期分布：数据不完整');
            return null;
        }
        
        // 找到日期列索引
        const dateColumnIndex = this.findDateColumnIndex(processedData.headers);
        if (dateColumnIndex === -1) {
            console.warn('未找到日期列，无法分析日期分布');
            return null;
        }
        
        // 统计日期分布
        const dateCount = new Map();
        const allDates = [];
        
        processedData.rows.forEach(row => {
            const dateValue = row[dateColumnIndex];
            if (dateValue) {
                const formattedDate = this.formatDateValue(dateValue);
                if (formattedDate instanceof Date) {
                    const dateKey = this.getDateGroupKey(dateValue);
                    dateCount.set(dateKey, (dateCount.get(dateKey) || 0) + 1);
                    allDates.push(formattedDate);
                }
            }
        });
        
        // 计算日期范围
        let minDate = null;
        let maxDate = null;
        if (allDates.length > 0) {
            minDate = new Date(Math.min(...allDates));
            maxDate = new Date(Math.max(...allDates));
        }
        
        const distribution = {
            dateCount,
            totalRecords: processedData.rows.length,
            dateRange: { min: minDate, max: maxDate },
            dateColumnIndex
        };
        
        console.log('日期分布分析完成:', distribution);
        return distribution;
    }
    
    /**
     * 格式化日期为显示字符串
     * @param {Date} date - 日期对象
     * @param {string} format - 格式字符串
     * @returns {string} 格式化后的日期字符串
     */
    static formatDateForDisplay(date, format = 'YYYY-MM-DD') {
        if (!(date instanceof Date) || isNaN(date.getTime())) {
            return '';
        }
        
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        
        switch (format) {
            case 'YYYY-MM-DD':
                return `${year}-${month}-${day}`;
            case 'YYYY/MM/DD':
                return `${year}/${month}/${day}`;
            case 'MM/DD/YYYY':
                return `${month}/${day}/${year}`;
            case 'DD/MM/YYYY':
                return `${day}/${month}/${year}`;
            default:
                return `${year}-${month}-${day}`;
        }
    }
    
    /**
     * 获取月份的天数
     * @param {number} year - 年份
     * @param {number} month - 月份（0-11）
     * @returns {number} 天数
     */
    static getDaysInMonth(year, month) {
        return new Date(year, month + 1, 0).getDate();
    }
    
    /**
     * 获取月份的第一天是星期几
     * @param {number} year - 年份
     * @param {number} month - 月份（0-11）
     * @returns {number} 星期几（0-6，0为星期日）
     */
    static getFirstDayOfMonth(year, month) {
        return new Date(year, month, 1).getDay();
    }
    
    /**
     * 检查两个日期是否为同一天
     * @param {Date} date1 - 日期1
     * @param {Date} date2 - 日期2
     * @returns {boolean} 是否为同一天
     */
    static isSameDay(date1, date2) {
        if (!(date1 instanceof Date) || !(date2 instanceof Date)) {
            return false;
        }
        
        return date1.getFullYear() === date2.getFullYear() &&
               date1.getMonth() === date2.getMonth() &&
               date1.getDate() === date2.getDate();
    }
    
    /**
     * 获取日期范围内的所有日期
     * @param {Date} startDate - 开始日期
     * @param {Date} endDate - 结束日期
     * @returns {Array<Date>} 日期数组
     */
    static getDateRange(startDate, endDate) {
        const dates = [];
        const currentDate = new Date(startDate);
        
        while (currentDate <= endDate) {
            dates.push(new Date(currentDate));
            currentDate.setDate(currentDate.getDate() + 1);
        }
        
        return dates;
    }
}

export default DateUtils;
