/**
 * UI控制模块
 * 负责UI更新、事件监听和用户交互
 */

import { DOM_IDS, CSS_CLASSES, UI_CONFIG } from '../utils/constants.js';
import ErrorHandler from '../utils/ErrorHandler.js';

/**
 * UI控制器类
 */
export class UIController {
    constructor() {
        this.elements = {};
        this.eventListeners = new Map();
        this.initialized = false;
    }
    
    /**
     * 初始化UI控制器
     */
    initialize() {
        if (this.initialized) {
            console.warn('UIController already initialized');
            return;
        }
        
        this.cacheElements();
        this.setupEventListeners();
        this.initialized = true;
        
        console.log('UIController initialized');
    }
    
    /**
     * 缓存DOM元素
     */
    cacheElements() {
        // 缓存所有重要的DOM元素
        Object.values(DOM_IDS).forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                this.elements[id] = element;
            } else {
                console.warn(`Element with ID '${id}' not found`);
            }
        });
    }
    
    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 文件选择事件
        this.addEventListener(DOM_IDS.FILE_INPUT, 'change', this.handleFileSelect.bind(this));
        
        // 全局拖拽事件
        this.addEventListener(document.body, 'dragover', this.handleDragOver.bind(this));
        this.addEventListener(document.body, 'dragleave', this.handleDragLeave.bind(this));
        this.addEventListener(document.body, 'drop', this.handleFileDrop.bind(this));
        
        // 转换按钮事件
        this.addEventListener(DOM_IDS.CONVERT_BTN, 'click', this.handleConvert.bind(this));
        
        // 月视图事件
        this.addEventListener(DOM_IDS.PREV_MONTH_BTN, 'click', this.handlePrevMonth.bind(this));
        this.addEventListener(DOM_IDS.NEXT_MONTH_BTN, 'click', this.handleNextMonth.bind(this));
        this.addEventListener(DOM_IDS.CLEAR_SELECTION_BTN, 'click', this.handleClearSelection.bind(this));
        this.addEventListener(DOM_IDS.PREVIEW_DATA_BTN, 'click', this.handlePreviewData.bind(this));
        this.addEventListener(DOM_IDS.EXPORT_FILTERED_BTN, 'click', this.handleExportFiltered.bind(this));
    }
    
    /**
     * 添加事件监听器
     * @param {string|Element} elementOrId - 元素ID或元素对象
     * @param {string} event - 事件类型
     * @param {Function} handler - 事件处理函数
     */
    addEventListener(elementOrId, event, handler) {
        let element;
        
        if (typeof elementOrId === 'string') {
            element = this.elements[elementOrId] || document.getElementById(elementOrId);
        } else {
            element = elementOrId;
        }
        
        if (element) {
            element.addEventListener(event, handler);
            
            // 记录事件监听器以便后续清理
            const key = `${elementOrId}-${event}`;
            this.eventListeners.set(key, { element, event, handler });
        } else {
            console.warn(`Cannot add event listener: element not found for ${elementOrId}`);
        }
    }
    
    /**
     * 移除事件监听器
     * @param {string|Element} elementOrId - 元素ID或元素对象
     * @param {string} event - 事件类型
     */
    removeEventListener(elementOrId, event) {
        const key = `${elementOrId}-${event}`;
        const listener = this.eventListeners.get(key);
        
        if (listener) {
            listener.element.removeEventListener(listener.event, listener.handler);
            this.eventListeners.delete(key);
        }
    }
    
    /**
     * 清理所有事件监听器
     */
    cleanup() {
        this.eventListeners.forEach((listener, key) => {
            listener.element.removeEventListener(listener.event, listener.handler);
        });
        this.eventListeners.clear();
        this.initialized = false;
    }
    
    /**
     * 获取元素
     * @param {string} id - 元素ID
     * @returns {Element|null} DOM元素
     */
    getElement(id) {
        return this.elements[id] || document.getElementById(id);
    }
    
    /**
     * 显示/隐藏元素
     * @param {string} id - 元素ID
     * @param {boolean} show - 是否显示
     */
    toggleElement(id, show) {
        const element = this.getElement(id);
        if (element) {
            element.style.display = show ? 'block' : 'none';
        }
    }
    
    /**
     * 设置元素文本内容
     * @param {string} id - 元素ID
     * @param {string} text - 文本内容
     */
    setText(id, text) {
        const element = this.getElement(id);
        if (element) {
            element.textContent = text;
        }
    }
    
    /**
     * 设置元素HTML内容
     * @param {string} id - 元素ID
     * @param {string} html - HTML内容
     */
    setHTML(id, html) {
        const element = this.getElement(id);
        if (element) {
            element.innerHTML = html;
        }
    }
    
    /**
     * 添加CSS类
     * @param {string} id - 元素ID
     * @param {string} className - CSS类名
     */
    addClass(id, className) {
        const element = this.getElement(id);
        if (element) {
            element.classList.add(className);
        }
    }
    
    /**
     * 移除CSS类
     * @param {string} id - 元素ID
     * @param {string} className - CSS类名
     */
    removeClass(id, className) {
        const element = this.getElement(id);
        if (element) {
            element.classList.remove(className);
        }
    }
    
    /**
     * 切换CSS类
     * @param {string} id - 元素ID
     * @param {string} className - CSS类名
     */
    toggleClass(id, className) {
        const element = this.getElement(id);
        if (element) {
            element.classList.toggle(className);
        }
    }
    
    /**
     * 显示消息
     * @param {string} text - 消息文本
     * @param {string} type - 消息类型
     */
    showMessage(text, type = 'info') {
        ErrorHandler.showMessage(text, type);
    }
    
    /**
     * 显示进度条
     */
    showProgress() {
        ErrorHandler.showProgress();
    }
    
    /**
     * 隐藏进度条
     */
    hideProgress() {
        ErrorHandler.hideProgress();
    }
    
    /**
     * 更新进度条
     * @param {number} percentage - 进度百分比
     * @param {string} text - 进度文本
     */
    updateProgress(percentage, text) {
        ErrorHandler.updateProgress(percentage, text);
    }
    
    /**
     * 显示文件信息
     * @param {File} file - 文件对象
     */
    displayFileInfo(file) {
        this.setText(DOM_IDS.FILE_NAME, file.name);
        this.setText(DOM_IDS.FILE_SIZE, this.formatFileSize(file.size));
        this.setText(DOM_IDS.FILE_DATE, new Date(file.lastModified).toLocaleString('zh-CN'));
        
        this.toggleElement(DOM_IDS.FILE_INFO, true);
        this.toggleElement(DOM_IDS.CONVERT_BTN, true);
    }
    
    /**
     * 格式化文件大小
     * @param {number} bytes - 字节数
     * @returns {string} 格式化后的文件大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    /**
     * 显示月视图
     */
    showMonthView() {
        this.toggleElement(DOM_IDS.MONTH_VIEW_SECTION, true);
    }
    
    /**
     * 隐藏月视图
     */
    hideMonthView() {
        this.toggleElement(DOM_IDS.MONTH_VIEW_SECTION, false);
    }
    
    /**
     * 更新统计信息
     * @param {Object} stats - 统计数据
     */
    updateStats(stats) {
        if (stats.totalRecords !== undefined) {
            this.setText(DOM_IDS.TOTAL_RECORDS, stats.totalRecords);
        }
        
        if (stats.dateRange) {
            const { min, max } = stats.dateRange;
            if (min && max) {
                const minStr = min.toLocaleDateString('zh-CN');
                const maxStr = max.toLocaleDateString('zh-CN');
                this.setText(DOM_IDS.DATE_RANGE, `${minStr} - ${maxStr}`);
            }
        }
        
        if (stats.selectedCount !== undefined) {
            this.setText(DOM_IDS.SELECTED_COUNT, stats.selectedCount);
        }
    }
    
    // ==================== 事件处理函数 ====================
    
    /**
     * 处理文件选择事件
     * @param {Event} event - 文件选择事件
     */
    handleFileSelect(event) {
        const file = event.target.files[0];
        if (file && this.onFileSelect) {
            this.onFileSelect(file);
        }
    }
    
    /**
     * 处理拖拽悬停事件
     * @param {DragEvent} event - 拖拽事件
     */
    handleDragOver(event) {
        event.preventDefault();
        document.body.style.backgroundColor = UI_CONFIG.DRAG_OVER_BACKGROUND;
    }
    
    /**
     * 处理拖拽离开事件
     * @param {DragEvent} event - 拖拽事件
     */
    handleDragLeave(event) {
        event.preventDefault();
        document.body.style.backgroundColor = UI_CONFIG.DRAG_LEAVE_BACKGROUND;
    }
    
    /**
     * 处理文件拖拽放置事件
     * @param {DragEvent} event - 拖拽事件
     */
    handleFileDrop(event) {
        event.preventDefault();
        document.body.style.backgroundColor = UI_CONFIG.DRAG_LEAVE_BACKGROUND;
        
        const files = event.dataTransfer.files;
        if (files.length > 0 && this.onFileSelect) {
            this.onFileSelect(files[0]);
        }
    }
    
    /**
     * 处理转换按钮点击事件
     * @param {Event} event - 点击事件
     */
    handleConvert(event) {
        if (this.onConvert) {
            this.onConvert();
        }
    }
    
    /**
     * 处理上一月按钮点击事件
     * @param {Event} event - 点击事件
     */
    handlePrevMonth(event) {
        if (this.onPrevMonth) {
            this.onPrevMonth();
        }
    }
    
    /**
     * 处理下一月按钮点击事件
     * @param {Event} event - 点击事件
     */
    handleNextMonth(event) {
        if (this.onNextMonth) {
            this.onNextMonth();
        }
    }
    
    /**
     * 处理清除选择按钮点击事件
     * @param {Event} event - 点击事件
     */
    handleClearSelection(event) {
        if (this.onClearSelection) {
            this.onClearSelection();
        }
    }
    
    /**
     * 处理预览数据按钮点击事件
     * @param {Event} event - 点击事件
     */
    handlePreviewData(event) {
        if (this.onPreviewData) {
            this.onPreviewData();
        }
    }
    
    /**
     * 处理导出过滤数据按钮点击事件
     * @param {Event} event - 点击事件
     */
    handleExportFiltered(event) {
        if (this.onExportFiltered) {
            this.onExportFiltered();
        }
    }
    
    // ==================== 回调函数设置 ====================
    
    /**
     * 设置文件选择回调
     * @param {Function} callback - 回调函数
     */
    setFileSelectCallback(callback) {
        this.onFileSelect = callback;
    }
    
    /**
     * 设置转换回调
     * @param {Function} callback - 回调函数
     */
    setConvertCallback(callback) {
        this.onConvert = callback;
    }
    
    /**
     * 设置月视图回调
     * @param {Object} callbacks - 回调函数对象
     */
    setCalendarCallbacks(callbacks) {
        this.onPrevMonth = callbacks.onPrevMonth;
        this.onNextMonth = callbacks.onNextMonth;
        this.onClearSelection = callbacks.onClearSelection;
        this.onPreviewData = callbacks.onPreviewData;
        this.onExportFiltered = callbacks.onExportFiltered;
    }
}

// 导出默认实例
export default new UIController();
