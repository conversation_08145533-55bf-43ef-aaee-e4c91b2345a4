// Excel文件处理Web Worker
// 这个Worker在后台处理Excel文件，避免阻塞主线程

// 导入ExcelJS库（需要在Worker中使用）
importScripts('https://cdn.jsdelivr.net/npm/exceljs@4.4.0/dist/exceljs.min.js');

// 监听主线程消息
self.onmessage = async function(e) {
    const { type, data } = e.data;
    
    try {
        switch (type) {
            case 'PROCESS_EXCEL_FILE':
                await processExcelFile(data);
                break;
            case 'READ_FILE_CHUNKS':
                await readFileInChunks(data);
                break;
            default:
                throw new Error(`未知的消息类型: ${type}`);
        }
    } catch (error) {
        self.postMessage({
            type: 'ERROR',
            error: error.message
        });
    }
};

// 处理Excel文件
async function processExcelFile({ arrayBuffer, fileName }) {
    try {
        // 发送开始处理消息
        self.postMessage({
            type: 'PROGRESS',
            progress: 0,
            message: '开始解析Excel文件...'
        });

        const workbook = new ExcelJS.Workbook();
        await workbook.xlsx.load(arrayBuffer);

        self.postMessage({
            type: 'PROGRESS',
            progress: 30,
            message: '正在提取工作表数据...'
        });

        // 获取第一个工作表
        const worksheet = workbook.worksheets[0];
        if (!worksheet) {
            throw new Error('工作簿中没有找到工作表');
        }

        self.postMessage({
            type: 'PROGRESS',
            progress: 50,
            message: '正在处理数据行...'
        });

        // 提取数据
        const data = [];
        const maxColumns = worksheet.columnCount || 10;
        let processedRows = 0;
        const totalRows = worksheet.rowCount;

        worksheet.eachRow((row, rowNumber) => {
            const rowData = new Array(maxColumns);

            // 只遍历实际有数据的列
            row.eachCell({ includeEmpty: false }, (cell, colNumber) => {
                if (colNumber <= maxColumns) {
                    rowData[colNumber - 1] = cell.value;
                }
            });

            data.push(rowData);
            processedRows++;

            // 定期发送进度更新
            if (processedRows % 100 === 0) {
                const progress = 50 + Math.round((processedRows / totalRows) * 40);
                self.postMessage({
                    type: 'PROGRESS',
                    progress: progress,
                    message: `正在处理数据行... ${processedRows}/${totalRows}`
                });
            }
        });

        self.postMessage({
            type: 'PROGRESS',
            progress: 90,
            message: '正在整理数据格式...'
        });

        // 处理数据格式
        const processedData = processDataRows(data);

        self.postMessage({
            type: 'PROGRESS',
            progress: 100,
            message: '文件处理完成！'
        });

        // 发送处理结果
        self.postMessage({
            type: 'SUCCESS',
            data: processedData
        });

    } catch (error) {
        self.postMessage({
            type: 'ERROR',
            error: error.message
        });
    }
}

// 分块读取文件
async function readFileInChunks({ file, chunkSize = 1024 * 1024 }) {
    const totalChunks = Math.ceil(file.size / chunkSize);
    const chunks = [];

    for (let i = 0; i < totalChunks; i++) {
        const start = i * chunkSize;
        const end = Math.min(start + chunkSize, file.size);
        const blob = file.slice(start, end);

        const arrayBuffer = await new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = () => reject(new Error('文件读取失败'));
            reader.readAsArrayBuffer(blob);
        });

        chunks.push(arrayBuffer);

        // 发送进度更新
        const progress = Math.round(((i + 1) / totalChunks) * 100);
        self.postMessage({
            type: 'READ_PROGRESS',
            progress: progress,
            message: `正在读取文件... ${progress}%`
        });
    }

    // 合并所有块
    const totalLength = chunks.reduce((sum, chunk) => sum + chunk.byteLength, 0);
    const result = new ArrayBuffer(totalLength);
    const view = new Uint8Array(result);
    let offset = 0;

    for (const chunk of chunks) {
        view.set(new Uint8Array(chunk), offset);
        offset += chunk.byteLength;
    }

    self.postMessage({
        type: 'READ_SUCCESS',
        arrayBuffer: result
    });
}

// 处理数据行（简化版本，主要逻辑在主线程）
function processDataRows(data) {
    if (data.length === 0) {
        throw new Error('工作表中没有数据');
    }

    // 智能检测表头位置
    let headers = null;
    let dataStartRow = 1;

    // 检查第一行是否是合并的标题行
    if (data[0]) {
        const firstRowNonEmptyCells = data[0].filter(cell =>
            cell !== null && cell !== undefined && String(cell).trim() !== ''
        );

        if (firstRowNonEmptyCells.length > 0) {
            const firstCellValue = String(firstRowNonEmptyCells[0]).trim();
            const allSame = firstRowNonEmptyCells.every(cell =>
                String(cell).trim() === firstCellValue
            );

            if (allSame && firstCellValue.includes('科陆流水线')) {
                headers = data[1];
                dataStartRow = 2;
            } else {
                headers = data[0];
                dataStartRow = 1;
            }
        } else {
            headers = data[0];
            dataStartRow = 1;
        }
    }

    if (!headers || headers.length === 0) {
        throw new Error('无法读取表头信息');
    }

    // 提取数据行
    const rows = [];
    for (let i = dataStartRow; i < data.length; i++) {
        const row = data[i];
        if (row && row.length > 0 && row.some(cell => cell !== undefined && cell !== '')) {
            rows.push(row);
        }
    }

    return {
        headers: headers,
        rows: rows,
        totalRows: rows.length
    };
}
