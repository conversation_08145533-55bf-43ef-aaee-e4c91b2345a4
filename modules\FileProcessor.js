/**
 * 文件处理模块
 * 负责文件上传、验证、读取等操作
 */

import { FILE_CONFIG, INFO_MESSAGES } from '../utils/constants.js';
import ErrorHandler, { FileProcessingError } from '../utils/ErrorHandler.js';

/**
 * 文件处理器类
 */
export class FileProcessor {
    constructor() {
        this.currentFile = null;
        this.onProgress = null;
    }
    
    /**
     * 设置进度回调
     * @param {Function} callback - 进度回调函数
     */
    setProgressCallback(callback) {
        this.onProgress = callback;
    }
    
    /**
     * 验证文件
     * @param {File} file - 文件对象
     * @returns {Object} 验证结果 {isValid: boolean, error?: string}
     */
    validateFile(file) {
        console.log('FileProcessor.validateFile called with:', file);
        try {
            ErrorHandler.validateFile(file);
            console.log('File validation passed');
            return { isValid: true };
        } catch (error) {
            console.log('File validation failed:', error.message);
            ErrorHandler.handleFileError(error, file?.name);
            return { isValid: false, error: error.message };
        }
    }
    
    /**
     * 处理文件选择
     * @param {File} file - 选择的文件
     * @returns {Promise<boolean>} 处理结果
     */
    async processFileSelection(file) {
        try {
            if (!this.validateFile(file)) {
                return false;
            }
            
            this.currentFile = file;
            this.displayFileInfo(file);
            
            ErrorHandler.showMessage(INFO_MESSAGES.READING_FILE, 'info');
            return true;
        } catch (error) {
            ErrorHandler.handleFileError(error, file?.name);
            return false;
        }
    }
    
    /**
     * 读取文件为ArrayBuffer
     * @param {File} file - 文件对象
     * @returns {Promise<ArrayBuffer>} 文件内容
     */
    async readFileAsArrayBuffer(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            
            reader.onload = (e) => {
                resolve(e.target.result);
                // 清理引用以释放内存
                reader.onload = null;
                reader.onerror = null;
            };
            
            reader.onerror = () => {
                const error = new FileProcessingError('文件读取失败', file.name);
                reject(error);
                // 清理引用
                reader.onload = null;
                reader.onerror = null;
            };
            
            reader.readAsArrayBuffer(file);
        });
    }
    
    /**
     * 分块读取文件（用于大文件）
     * @param {File} file - 文件对象
     * @returns {Promise<ArrayBuffer>} 文件内容
     */
    async readFileWithProgress(file) {
        return new Promise((resolve, reject) => {
            const chunkSize = FILE_CONFIG.CHUNK_SIZE;
            const totalChunks = Math.ceil(file.size / chunkSize);
            let currentChunk = 0;
            const chunks = [];
            
            const readNextChunk = () => {
                if (currentChunk >= totalChunks) {
                    // 合并所有块
                    const totalLength = chunks.reduce((sum, chunk) => sum + chunk.byteLength, 0);
                    const result = new ArrayBuffer(totalLength);
                    const view = new Uint8Array(result);
                    let offset = 0;
                    
                    for (const chunk of chunks) {
                        view.set(new Uint8Array(chunk), offset);
                        offset += chunk.byteLength;
                    }
                    
                    resolve(result);
                    return;
                }
                
                const start = currentChunk * chunkSize;
                const end = Math.min(start + chunkSize, file.size);
                const blob = file.slice(start, end);
                
                const reader = new FileReader();
                reader.onload = (e) => {
                    chunks.push(e.target.result);
                    currentChunk++;
                    
                    // 更新进度
                    const progress = Math.round((currentChunk / totalChunks) * 100);
                    if (this.onProgress) {
                        this.onProgress(progress, `正在读取文件... ${progress}%`);
                    }
                    
                    // 使用 setTimeout 让UI有机会更新
                    setTimeout(readNextChunk, 10);
                };
                
                reader.onerror = () => {
                    reject(new FileProcessingError('文件读取失败', file.name));
                };
                
                reader.readAsArrayBuffer(blob);
            };
            
            readNextChunk();
        });
    }
    
    /**
     * 根据文件大小选择合适的读取方式
     * @param {File} file - 文件对象
     * @returns {Promise<ArrayBuffer>} 文件内容
     */
    async readFile(file) {
        try {
            const fileSizeMB = file.size / (1024 * 1024);
            
            if (fileSizeMB > 5) {
                // 大文件使用分块读取
                return await this.readFileWithProgress(file);
            } else {
                // 小文件使用普通读取
                return await this.readFileAsArrayBuffer(file);
            }
        } catch (error) {
            throw new FileProcessingError('文件读取失败', file.name, error);
        }
    }
    
    /**
     * 显示文件信息
     * @param {File} file - 文件对象
     */
    displayFileInfo(file) {
        try {
            const fileNameElement = document.getElementById('fileName');
            const fileSizeElement = document.getElementById('fileSize');
            const fileDateElement = document.getElementById('fileDate');
            const fileInfoElement = document.getElementById('fileInfo');
            const convertBtnElement = document.getElementById('convertBtn');
            
            if (fileNameElement) {
                fileNameElement.textContent = file.name;
            }
            
            if (fileSizeElement) {
                fileSizeElement.textContent = this.formatFileSize(file.size);
            }
            
            if (fileDateElement) {
                fileDateElement.textContent = new Date(file.lastModified).toLocaleString('zh-CN');
            }
            
            if (fileInfoElement) {
                fileInfoElement.style.display = 'block';
            }
            
            if (convertBtnElement) {
                convertBtnElement.style.display = 'inline-block';
            }
        } catch (error) {
            console.warn('显示文件信息失败:', error);
        }
    }
    
    /**
     * 格式化文件大小
     * @param {number} bytes - 字节数
     * @returns {string} 格式化后的文件大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    /**
     * 处理拖拽事件
     * @param {DragEvent} event - 拖拽事件
     * @returns {File|null} 拖拽的文件
     */
    handleDragDrop(event) {
        event.preventDefault();
        
        const files = event.dataTransfer.files;
        if (files.length > 0) {
            return files[0];
        }
        
        return null;
    }
    
    /**
     * 处理拖拽悬停
     * @param {DragEvent} event - 拖拽事件
     */
    handleDragOver(event) {
        event.preventDefault();
        // 添加视觉反馈
        document.body.style.backgroundColor = '#e8f4fd';
    }
    
    /**
     * 处理拖拽离开
     * @param {DragEvent} event - 拖拽事件
     */
    handleDragLeave(event) {
        event.preventDefault();
        // 移除视觉反馈
        document.body.style.backgroundColor = '#f5f7fa';
    }
    
    /**
     * 获取当前文件
     * @returns {File|null} 当前文件
     */
    getCurrentFile() {
        return this.currentFile;
    }
    
    /**
     * 清除当前文件
     */
    clearCurrentFile() {
        this.currentFile = null;
    }
    
    /**
     * 检查是否支持Web Workers
     * @returns {boolean} 是否支持
     */
    static supportsWebWorkers() {
        return typeof Worker !== 'undefined';
    }
    
    /**
     * 检查文件是否为大文件
     * @param {File} file - 文件对象
     * @returns {boolean} 是否为大文件
     */
    static isLargeFile(file) {
        return file.size >= FILE_CONFIG.LARGE_FILE_THRESHOLD;
    }
}

// 导出默认实例
export default new FileProcessor();
