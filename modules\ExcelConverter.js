/**
 * Excel转换器主应用类
 * 负责协调各个模块，管理应用状态和业务流程
 */

import { FILE_CONFIG, INFO_MESSAGES, SUCCESS_MESSAGES } from '../utils/constants.js';
import ErrorHandler, { FileProcessingError, ExcelProcessingError } from '../utils/ErrorHandler.js';
import FileProcessor from './FileProcessor.js';
import ExcelHandler from './ExcelHandler.js';
import DateUtils from './DateUtils.js';
import UIController from './UIController.js';
import CalendarView from './CalendarView.js';

/**
 * Excel转换器主应用类
 */
export class ExcelConverter {
    constructor() {
        this.currentFile = null;
        this.processedData = null;
        this.dateDistribution = null;
        this.isProcessing = false;
        this.initialized = false;
    }
    
    /**
     * 初始化应用
     */
    async initialize() {
        if (this.initialized) {
            console.warn('ExcelConverter already initialized');
            return;
        }
        
        try {
            // 初始化UI控制器
            UIController.initialize();
            
            // 设置UI回调
            this.setupUICallbacks();
            
            // 显示初始化完成消息
            ErrorHandler.showMessage(INFO_MESSAGES.APP_READY, 'success');
            
            this.initialized = true;
            console.log('ExcelConverter initialized successfully');
            
        } catch (error) {
            ErrorHandler.handle(error, 'initialize');
            throw error;
        }
    }
    
    /**
     * 设置UI回调函数
     */
    setupUICallbacks() {
        // 文件选择回调
        UIController.setFileSelectCallback(this.handleFileSelect.bind(this));
        
        // 转换回调
        UIController.setConvertCallback(this.handleConvert.bind(this));
        
        // 月历回调
        UIController.setCalendarCallbacks({
            onPrevMonth: this.handlePrevMonth.bind(this),
            onNextMonth: this.handleNextMonth.bind(this),
            onClearSelection: this.handleClearSelection.bind(this),
            onPreviewData: this.handlePreviewData.bind(this),
            onExportFiltered: this.handleExportFiltered.bind(this)
        });
        
        // 月历日期选择回调
        CalendarView.setDateSelectCallbacks(
            this.handleDateSelect.bind(this),
            this.handleDateDeselect.bind(this)
        );
    }
    
    /**
     * 处理文件选择
     * @param {File} file - 选择的文件
     */
    async handleFileSelect(file) {
        if (this.isProcessing) {
            ErrorHandler.showMessage('正在处理文件，请稍候...', 'warning');
            return;
        }
        
        try {
            console.log('ExcelConverter.handleFileSelect called with file:', file);
            console.log('File details:', { name: file?.name, size: file?.size, type: file?.type });

            // 验证文件
            console.log('Calling FileProcessor.validateFile...');
            const validation = FileProcessor.validateFile(file);
            console.log('Validation result:', validation);

            if (!validation.isValid) {
                console.log('Validation failed, throwing error:', validation.error);
                throw new FileProcessingError(validation.error || '文件验证失败', file?.name || '');
            }

            console.log('File validation passed');
            
            // 保存当前文件
            this.currentFile = file;
            
            // 显示文件信息
            UIController.displayFileInfo(file);
            
            // 隐藏月视图（如果之前显示过）
            UIController.hideMonthView();
            
            ErrorHandler.showMessage(INFO_MESSAGES.FILE_SELECTED, 'success');
            
        } catch (error) {
            ErrorHandler.handle(error, 'file-select');
        }
    }
    
    /**
     * 处理文件转换
     */
    async handleConvert() {
        if (!this.currentFile) {
            ErrorHandler.showMessage('请先选择文件', 'warning');
            return;
        }
        
        if (this.isProcessing) {
            ErrorHandler.showMessage('正在处理中，请稍候...', 'warning');
            return;
        }
        
        this.isProcessing = true;
        
        try {
            // 显示进度条
            UIController.showProgress();
            UIController.updateProgress(0, INFO_MESSAGES.READING_FILE);
            
            // 读取文件
            const arrayBuffer = await FileProcessor.readFileWithProgress(
                this.currentFile,
                (progress, message) => {
                    UIController.updateProgress(progress * 30, message); // 文件读取占30%
                }
            );
            
            UIController.updateProgress(30, INFO_MESSAGES.PARSING_EXCEL);
            
            // 处理Excel文件
            let workbook;
            if (this.currentFile.size > FILE_CONFIG.LARGE_FILE_THRESHOLD) {
                // 大文件使用Web Worker处理
                this.processedData = await ExcelHandler.readExcelFileWithWorker(arrayBuffer, this.currentFile.name);
            } else {
                // 小文件直接处理
                workbook = await ExcelHandler.readExcelFile(arrayBuffer, this.currentFile.name);
                UIController.updateProgress(60, INFO_MESSAGES.PROCESSING_DATA);
                this.processedData = await ExcelHandler.processWorkbookData(workbook);
            }
            
            UIController.updateProgress(80, INFO_MESSAGES.ANALYZING_DATES);
            
            // 分析日期分布
            this.dateDistribution = DateUtils.analyzeDateDistribution(this.processedData);
            
            UIController.updateProgress(90, INFO_MESSAGES.UPDATING_UI);
            
            // 更新UI
            this.updateUIAfterProcessing();
            
            UIController.updateProgress(100, SUCCESS_MESSAGES.PROCESSING_COMPLETE);
            
            // 延迟隐藏进度条
            setTimeout(() => {
                UIController.hideProgress();
                ErrorHandler.showMessage(SUCCESS_MESSAGES.PROCESSING_COMPLETE, 'success');
            }, 1000);
            
        } catch (error) {
            UIController.hideProgress();
            ErrorHandler.handle(error, 'convert');
        } finally {
            this.isProcessing = false;
        }
    }
    
    /**
     * 处理完成后更新UI
     */
    updateUIAfterProcessing() {
        if (!this.processedData || !this.dateDistribution) {
            return;
        }
        
        // 更新统计信息
        UIController.updateStats({
            totalRecords: this.dateDistribution.totalRecords,
            dateRange: this.dateDistribution.dateRange
        });
        
        // 初始化并显示月历视图
        CalendarView.initialize(this.dateDistribution);
        UIController.showMonthView();
        
        console.log('UI updated after processing:', {
            totalRecords: this.dateDistribution.totalRecords,
            dateRange: this.dateDistribution.dateRange
        });
    }
    
    /**
     * 处理上一月按钮点击
     */
    handlePrevMonth() {
        CalendarView.showPreviousMonth();
    }
    
    /**
     * 处理下一月按钮点击
     */
    handleNextMonth() {
        CalendarView.showNextMonth();
    }
    
    /**
     * 处理清除选择按钮点击
     */
    handleClearSelection() {
        CalendarView.clearSelection();
        UIController.updateStats({ selectedCount: 0 });
    }
    
    /**
     * 处理日期选择
     * @param {string} dateKey - 日期键
     */
    handleDateSelect(dateKey) {
        const stats = CalendarView.getSelectedDatesStats();
        UIController.updateStats({ selectedCount: stats.selectedCount });
        
        console.log('Date selected:', dateKey, 'Total selected:', stats.selectedCount);
    }
    
    /**
     * 处理日期取消选择
     * @param {string} dateKey - 日期键
     */
    handleDateDeselect(dateKey) {
        const stats = CalendarView.getSelectedDatesStats();
        UIController.updateStats({ selectedCount: stats.selectedCount });
        
        console.log('Date deselected:', dateKey, 'Total selected:', stats.selectedCount);
    }
    
    /**
     * 处理预览数据按钮点击
     */
    async handlePreviewData() {
        if (!this.processedData) {
            ErrorHandler.showMessage('没有可预览的数据', 'warning');
            return;
        }
        
        try {
            const selectedDates = CalendarView.getSelectedDates();
            
            if (selectedDates.length === 0) {
                // 预览所有数据
                await this.previewAllData();
            } else {
                // 预览选中日期的数据
                await this.previewFilteredData(selectedDates);
            }
            
        } catch (error) {
            ErrorHandler.handle(error, 'preview');
        }
    }
    
    /**
     * 预览所有数据
     */
    async previewAllData() {
        try {
            UIController.showProgress();
            UIController.updateProgress(50, '正在生成预览...');
            
            const workbook = await ExcelHandler.createFormattedWorkbook(this.processedData);
            
            // 这里可以添加预览逻辑，比如在新窗口中显示
            console.log('Preview all data:', this.processedData);
            
            UIController.updateProgress(100, '预览生成完成');
            setTimeout(() => {
                UIController.hideProgress();
                ErrorHandler.showMessage('预览生成完成', 'success');
            }, 500);
            
        } catch (error) {
            UIController.hideProgress();
            throw error;
        }
    }
    
    /**
     * 预览过滤后的数据
     * @param {Array<string>} selectedDates - 选中的日期
     */
    async previewFilteredData(selectedDates) {
        try {
            UIController.showProgress();
            UIController.updateProgress(30, '正在过滤数据...');
            
            const filteredData = this.filterDataByDates(selectedDates);
            
            UIController.updateProgress(70, '正在生成预览...');
            
            const workbook = await ExcelHandler.createFormattedWorkbook(filteredData);
            
            console.log('Preview filtered data:', filteredData);
            
            UIController.updateProgress(100, '预览生成完成');
            setTimeout(() => {
                UIController.hideProgress();
                ErrorHandler.showMessage(`已生成${selectedDates.length}个日期的数据预览`, 'success');
            }, 500);
            
        } catch (error) {
            UIController.hideProgress();
            throw error;
        }
    }
    
    /**
     * 处理导出过滤数据按钮点击
     */
    async handleExportFiltered() {
        if (!this.processedData) {
            ErrorHandler.showMessage('没有可导出的数据', 'warning');
            return;
        }
        
        const selectedDates = CalendarView.getSelectedDates();
        
        if (selectedDates.length === 0) {
            ErrorHandler.showMessage('请先选择要导出的日期', 'warning');
            return;
        }
        
        try {
            UIController.showProgress();
            UIController.updateProgress(20, '正在过滤数据...');
            
            const filteredData = this.filterDataByDates(selectedDates);
            
            UIController.updateProgress(50, '正在生成Excel文件...');
            
            const workbook = await ExcelHandler.createFormattedWorkbook(filteredData);
            
            UIController.updateProgress(80, '正在下载文件...');
            
            const fileName = await ExcelHandler.downloadWorkbook(workbook);
            
            UIController.updateProgress(100, '导出完成');
            
            setTimeout(() => {
                UIController.hideProgress();
                ErrorHandler.showMessage(`已导出${selectedDates.length}个日期的数据到文件：${fileName}`, 'success');
            }, 500);
            
        } catch (error) {
            UIController.hideProgress();
            ErrorHandler.handle(error, 'export');
        }
    }
    
    /**
     * 根据选中日期过滤数据
     * @param {Array<string>} selectedDates - 选中的日期键
     * @returns {Object} 过滤后的数据
     */
    filterDataByDates(selectedDates) {
        if (!this.processedData || !this.dateDistribution) {
            throw new Error('没有可过滤的数据');
        }
        
        const dateColumnIndex = this.dateDistribution.dateColumnIndex;
        const selectedDateSet = new Set(selectedDates);
        
        // 过滤数据行
        const filteredRows = this.processedData.rows.filter(row => {
            const dateValue = row[dateColumnIndex];
            const dateKey = DateUtils.getDateGroupKey(dateValue);
            return selectedDateSet.has(dateKey);
        });
        
        // 过滤合并信息
        const filteredMergeInfo = this.processedData.mergeInfo ? 
            this.processedData.mergeInfo.filter(merge => {
                // 这里需要根据实际情况调整合并信息的过滤逻辑
                return true; // 简化处理，保留所有合并信息
            }) : [];
        
        return {
            headers: this.processedData.headers,
            rows: filteredRows,
            mergeInfo: filteredMergeInfo
        };
    }
    
    /**
     * 获取应用状态
     * @returns {Object} 应用状态
     */
    getState() {
        return {
            initialized: this.initialized,
            isProcessing: this.isProcessing,
            hasFile: !!this.currentFile,
            hasProcessedData: !!this.processedData,
            hasDateDistribution: !!this.dateDistribution,
            selectedDatesCount: CalendarView.hasSelectedDates() ? CalendarView.getSelectedDates().length : 0
        };
    }
    
    /**
     * 重置应用状态
     */
    reset() {
        this.currentFile = null;
        this.processedData = null;
        this.dateDistribution = null;
        this.isProcessing = false;
        
        // 重置UI
        UIController.hideMonthView();
        UIController.hideProgress();
        CalendarView.destroy();
        
        ErrorHandler.showMessage('应用已重置', 'info');
    }
    
    /**
     * 销毁应用
     */
    destroy() {
        this.reset();
        UIController.cleanup();
        CalendarView.destroy();
        this.initialized = false;
        
        console.log('ExcelConverter destroyed');
    }
}

// 导出默认实例
export default new ExcelConverter();
