/**
 * 月视图模块
 * 负责月历显示和日期选择功能
 */

import { DOM_IDS, CSS_CLASSES } from '../utils/constants.js';
import DateUtils from './DateUtils.js';
import ErrorHandler from '../utils/ErrorHandler.js';

/**
 * 月历视图类
 */
export class CalendarView {
    constructor() {
        this.currentDate = new Date();
        this.selectedDates = new Set();
        this.dateDistribution = null;
        this.onDateSelect = null;
        this.onDateDeselect = null;
    }
    
    /**
     * 初始化月历视图
     * @param {Object} dateDistribution - 日期分布数据
     */
    initialize(dateDistribution) {
        this.dateDistribution = dateDistribution;
        
        if (dateDistribution && dateDistribution.dateRange.min) {
            this.currentDate = new Date(dateDistribution.dateRange.min);
        }
        
        this.render();
        this.updateSelectedDatesDisplay();
    }
    
    /**
     * 设置日期分布数据
     * @param {Object} dateDistribution - 日期分布数据
     */
    setDateDistribution(dateDistribution) {
        this.dateDistribution = dateDistribution;
        this.render();
    }
    
    /**
     * 渲染月历
     */
    render() {
        const calendarContainer = document.getElementById(DOM_IDS.CALENDAR_CONTAINER);
        const currentMonthElement = document.getElementById(DOM_IDS.CURRENT_MONTH);
        
        if (!calendarContainer || !currentMonthElement) {
            console.warn('Calendar container or current month element not found');
            return;
        }
        
        // 更新当前月份显示
        currentMonthElement.textContent = this.formatCurrentMonth();
        
        // 生成月历HTML
        const calendarHTML = this.generateCalendarHTML();
        calendarContainer.innerHTML = calendarHTML;
        
        // 添加日期点击事件
        this.attachDateClickEvents(calendarContainer);
    }
    
    /**
     * 格式化当前月份显示
     * @returns {string} 格式化的月份字符串
     */
    formatCurrentMonth() {
        const year = this.currentDate.getFullYear();
        const month = this.currentDate.getMonth() + 1;
        return `${year}年${month}月`;
    }
    
    /**
     * 生成月历HTML
     * @returns {string} 月历HTML字符串
     */
    generateCalendarHTML() {
        const year = this.currentDate.getFullYear();
        const month = this.currentDate.getMonth();
        
        // 获取月份信息
        const daysInMonth = DateUtils.getDaysInMonth(year, month);
        const firstDayOfWeek = DateUtils.getFirstDayOfMonth(year, month);
        
        let html = '<div class="calendar-grid">';
        
        // 添加星期标题
        html += this.generateWeekHeaderHTML();
        
        // 添加日期单元格
        html += this.generateDateCellsHTML(year, month, daysInMonth, firstDayOfWeek);
        
        html += '</div>';
        
        return html;
    }
    
    /**
     * 生成星期标题HTML
     * @returns {string} 星期标题HTML
     */
    generateWeekHeaderHTML() {
        const weekDays = ['日', '一', '二', '三', '四', '五', '六'];
        let html = '';
        
        weekDays.forEach(day => {
            html += `<div class="calendar-header">${day}</div>`;
        });
        
        return html;
    }
    
    /**
     * 生成日期单元格HTML
     * @param {number} year - 年份
     * @param {number} month - 月份
     * @param {number} daysInMonth - 月份天数
     * @param {number} firstDayOfWeek - 第一天是星期几
     * @returns {string} 日期单元格HTML
     */
    generateDateCellsHTML(year, month, daysInMonth, firstDayOfWeek) {
        let html = '';
        
        // 添加上个月的空白日期
        for (let i = 0; i < firstDayOfWeek; i++) {
            html += '<div class="calendar-day other-month"></div>';
        }
        
        // 添加当前月份的日期
        for (let day = 1; day <= daysInMonth; day++) {
            const date = new Date(year, month, day);
            const dateKey = DateUtils.getDateGroupKey(date);
            
            // 检查是否有数据
            const hasData = this.dateDistribution && this.dateDistribution.dateCount.has(dateKey);
            const recordCount = hasData ? this.dateDistribution.dateCount.get(dateKey) : 0;
            
            // 检查是否被选中
            const isSelected = this.selectedDates.has(dateKey);
            
            // 生成CSS类
            const classes = this.generateDateCellClasses(hasData, isSelected);
            
            // 生成日期单元格
            html += `<div class="${classes}" data-date="${dateKey}" data-year="${year}" data-month="${month}" data-day="${day}">`;
            html += `<span class="day-number">${day}</span>`;
            
            if (hasData) {
                html += `<span class="record-count">${recordCount}</span>`;
            }
            
            html += '</div>';
        }
        
        return html;
    }
    
    /**
     * 生成日期单元格CSS类
     * @param {boolean} hasData - 是否有数据
     * @param {boolean} isSelected - 是否被选中
     * @returns {string} CSS类字符串
     */
    generateDateCellClasses(hasData, isSelected) {
        let classes = ['calendar-day'];
        
        if (hasData) {
            classes.push('has-data');
        }
        
        if (isSelected) {
            classes.push('selected');
        }
        
        return classes.join(' ');
    }
    
    /**
     * 添加日期点击事件
     * @param {Element} container - 月历容器元素
     */
    attachDateClickEvents(container) {
        const dateCells = container.querySelectorAll('.calendar-day.has-data');
        
        dateCells.forEach(cell => {
            cell.addEventListener('click', (event) => {
                this.handleDateClick(event.currentTarget);
            });
        });
    }
    
    /**
     * 处理日期点击事件
     * @param {Element} dateCell - 日期单元格元素
     */
    handleDateClick(dateCell) {
        const dateKey = dateCell.dataset.date;
        
        if (!dateKey) return;
        
        if (this.selectedDates.has(dateKey)) {
            // 取消选择
            this.selectedDates.delete(dateKey);
            dateCell.classList.remove('selected');
            
            if (this.onDateDeselect) {
                this.onDateDeselect(dateKey);
            }
        } else {
            // 选择日期
            this.selectedDates.add(dateKey);
            dateCell.classList.add('selected');
            
            if (this.onDateSelect) {
                this.onDateSelect(dateKey);
            }
        }
        
        this.updateSelectedDatesDisplay();
    }
    
    /**
     * 更新选中日期显示
     */
    updateSelectedDatesDisplay() {
        const selectedCountElement = document.getElementById(DOM_IDS.SELECTED_COUNT);
        const selectedDatesListElement = document.getElementById(DOM_IDS.SELECTED_DATES_LIST);
        
        if (selectedCountElement) {
            selectedCountElement.textContent = this.selectedDates.size;
        }
        
        if (selectedDatesListElement) {
            if (this.selectedDates.size === 0) {
                selectedDatesListElement.innerHTML = '<span class="no-selection">未选择日期</span>';
            } else {
                const datesList = Array.from(this.selectedDates)
                    .sort()
                    .map(dateKey => `<span class="selected-date">${dateKey}</span>`)
                    .join('');
                selectedDatesListElement.innerHTML = datesList;
            }
        }
    }
    
    /**
     * 显示上一个月
     */
    showPreviousMonth() {
        this.currentDate.setMonth(this.currentDate.getMonth() - 1);
        this.render();
    }
    
    /**
     * 显示下一个月
     */
    showNextMonth() {
        this.currentDate.setMonth(this.currentDate.getMonth() + 1);
        this.render();
    }
    
    /**
     * 清除所有选中的日期
     */
    clearSelection() {
        this.selectedDates.clear();
        
        // 更新UI
        const selectedCells = document.querySelectorAll('.calendar-day.selected');
        selectedCells.forEach(cell => {
            cell.classList.remove('selected');
        });
        
        this.updateSelectedDatesDisplay();
    }
    
    /**
     * 选择日期范围
     * @param {Date} startDate - 开始日期
     * @param {Date} endDate - 结束日期
     */
    selectDateRange(startDate, endDate) {
        const dates = DateUtils.getDateRange(startDate, endDate);
        
        dates.forEach(date => {
            const dateKey = DateUtils.getDateGroupKey(date);
            if (this.dateDistribution && this.dateDistribution.dateCount.has(dateKey)) {
                this.selectedDates.add(dateKey);
            }
        });
        
        this.render();
        this.updateSelectedDatesDisplay();
    }
    
    /**
     * 获取选中的日期
     * @returns {Array<string>} 选中的日期键数组
     */
    getSelectedDates() {
        return Array.from(this.selectedDates);
    }
    
    /**
     * 设置选中的日期
     * @param {Array<string>} dateKeys - 日期键数组
     */
    setSelectedDates(dateKeys) {
        this.selectedDates.clear();
        dateKeys.forEach(dateKey => {
            this.selectedDates.add(dateKey);
        });
        
        this.render();
        this.updateSelectedDatesDisplay();
    }
    
    /**
     * 检查是否有选中的日期
     * @returns {boolean} 是否有选中的日期
     */
    hasSelectedDates() {
        return this.selectedDates.size > 0;
    }
    
    /**
     * 获取选中日期的统计信息
     * @returns {Object} 统计信息
     */
    getSelectedDatesStats() {
        let totalRecords = 0;
        const selectedDatesList = Array.from(this.selectedDates);
        
        if (this.dateDistribution) {
            selectedDatesList.forEach(dateKey => {
                totalRecords += this.dateDistribution.dateCount.get(dateKey) || 0;
            });
        }
        
        return {
            selectedCount: this.selectedDates.size,
            totalRecords,
            datesList: selectedDatesList.sort()
        };
    }
    
    /**
     * 设置日期选择回调
     * @param {Function} onSelect - 选择回调
     * @param {Function} onDeselect - 取消选择回调
     */
    setDateSelectCallbacks(onSelect, onDeselect) {
        this.onDateSelect = onSelect;
        this.onDateDeselect = onDeselect;
    }
    
    /**
     * 跳转到指定月份
     * @param {Date} date - 目标日期
     */
    goToMonth(date) {
        this.currentDate = new Date(date);
        this.render();
    }
    
    /**
     * 跳转到今天所在的月份
     */
    goToToday() {
        this.currentDate = new Date();
        this.render();
    }
    
    /**
     * 获取当前显示的月份
     * @returns {Date} 当前月份
     */
    getCurrentMonth() {
        return new Date(this.currentDate);
    }
    
    /**
     * 销毁月历视图
     */
    destroy() {
        this.selectedDates.clear();
        this.dateDistribution = null;
        this.onDateSelect = null;
        this.onDateDeselect = null;
        
        const calendarContainer = document.getElementById(DOM_IDS.CALENDAR_CONTAINER);
        if (calendarContainer) {
            calendarContainer.innerHTML = '';
        }
    }
}

// 导出默认实例
export default new CalendarView();
