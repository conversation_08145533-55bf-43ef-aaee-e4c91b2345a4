# Excel日志转换器 - 科陆流水线运维日志处理系统

一个功能强大的基于Web的Excel文件处理工具，专门用于科陆流水线运维日志数据的智能化处理和分析。支持大文件处理、日期过滤、数据预览和批量导出等高级功能。

## ✨ 核心功能

### 📁 文件处理
- **智能上传**: 支持拖拽上传和点击选择，最大支持50MB文件
- **格式支持**: 专门优化的`.xlsx`文件处理
- **进度显示**: 实时显示文件处理进度和状态
- **错误处理**: 完善的错误提示和异常处理机制

### 📅 日期管理
- **月历视图**: 直观的月历界面显示数据分布
- **日期过滤**: 灵活选择特定日期范围的数据
- **批量选择**: 支持多日期选择和批量操作
- **统计信息**: 实时显示记录总数和日期范围

### � 数据处理
- **智能转换**: 按照预定义规则自动处理Excel数据
- **数据预览**: 处理前可预览数据内容和结构
- **格式化输出**: 生成符合要求的运维日志格式
- **自动命名**: 智能生成文件名：`科陆流水线运维日志YYYYMMDD.xlsx`

### ⚡ 性能优化
- **内存管理**: 智能内存管理和垃圾回收机制
- **批量处理**: 大数据集的分批处理能力
- **异步操作**: 非阻塞的文件处理流程
- **Web Workers**: 后台处理支持（可选）

## 🚀 使用方法

### 基本操作流程
1. **打开应用**: 在浏览器中打开 `index.html` 文件
2. **上传文件**:
   - 点击"选择文件"按钮选择Excel文件
   - 或直接拖拽`.xlsx`文件到页面任意位置
3. **查看数据**: 文件上传后自动显示月历视图和数据统计
4. **选择日期**: 在月历中点击选择需要导出的日期
5. **预览数据**: 点击"预览数据"查看选中日期的数据内容
6. **导出文件**: 点击"导出Excel"生成并下载处理后的文件

### 高级功能
- **月份导航**: 使用左右箭头切换不同月份
- **清除选择**: 一键清除所有已选日期
- **重新处理**: 对同一文件进行重新处理

## 🔧 数据转换规则

### 列处理规则
- ✅ **保留列**: 保留所有原始数据列（除"记录人"列）
- ❌ **移除列**: 自动移除"记录人"列
- ➕ **新增列**:
  - "备注"列（默认值："已解决"）
  - "维护保养情况"列（空白，供后续填写）

### 文件命名规则
- 格式：`科陆流水线运维日志YYYYMMDD.xlsx`
- 自动根据选择的日期范围生成合适的文件名
- 支持单日和多日期范围的命名

## 🛠️ 技术架构

### 前端技术栈
- **HTML5**: 现代化页面结构和语义化标签
- **CSS3**: 响应式设计和现代化UI样式
- **JavaScript (ES6+)**: 模块化的核心业务逻辑
- **ExcelJS**: 专业的Excel文件处理库

### 性能优化技术
- **内存管理器**: 自动内存清理和优化
- **任务队列**: 异步任务调度和并发控制
- **性能监控**: 实时性能指标监控
- **分块处理**: 大文件的分块读取和处理

### 架构特点
- **模块化设计**: 清晰的代码结构和职责分离
- **配置驱动**: 灵活的性能参数配置
- **事件驱动**: 响应式的用户交互处理
- **错误恢复**: 完善的错误处理和恢复机制

## 📁 项目结构

```
转换器/
├── index.html              # 主应用页面
├── styles.css              # 样式文件
├── script.js               # 核心业务逻辑 (2000+ 行)
├── performance-config.js   # 性能优化配置
├── excel-worker.js         # Web Worker处理器
├── README.md              # 项目文档
├── task.md                # 任务说明
├── 2.md                   # 开发文档
├── 日报客户.xlsx           # 示例数据文件
└── 文档/
    ├── 列宽行高优化实施报告.md
    ├── 性能优化说明.md
    ├── 文件自动命名功能实施报告.md
    ├── 文件读取优化说明.md
    └── 样式实施报告.md
```

## ⚙️ 配置说明

### 性能配置参数
```javascript
// 文件处理配置
maxSize: 50MB           // 最大文件大小
chunkSize: 1MB          // 分块处理大小
batchSize: 1000         // 批处理记录数

// 内存管理配置
gcInterval: 5000ms      // 垃圾回收间隔
maxCacheSize: 100MB     // 最大缓存大小

// UI响应配置
debounceDelay: 300ms    // 防抖延迟
throttleDelay: 100ms    // 节流延迟
```

## 🌐 浏览器兼容性

### 推荐浏览器
- **Chrome 80+** (推荐)
- **Firefox 75+**
- **Safari 13+**
- **Edge 80+**

### 功能要求
- 支持ES6+语法
- 支持File API
- 支持Web Workers (可选)
- 支持现代CSS特性

## 📋 系统要求

### 最低配置
- **内存**: 4GB RAM
- **存储**: 100MB可用空间
- **网络**: 用于加载ExcelJS库

### 推荐配置
- **内存**: 8GB+ RAM (处理大文件)
- **处理器**: 多核CPU (并行处理)
- **网络**: 稳定的互联网连接

## 🔒 安全特性

- **本地处理**: 所有数据处理都在浏览器端完成
- **无服务器**: 数据不会上传到任何服务器
- **隐私保护**: 文件内容完全保留在本地
- **安全传输**: 使用HTTPS加载外部库

## 📈 开发状态

### ✅ 已完成功能
- 完整的文件上传和处理系统
- 月历视图和日期选择功能
- 数据预览和过滤机制
- Excel文件读写和格式转换
- 性能优化和内存管理
- 进度显示和状态管理
- 错误处理和用户反馈
- 响应式UI设计

### 🚧 开发中功能
- Web Workers并行处理优化
- 更多数据格式支持
- 高级过滤和搜索功能

### 📋 后续计划
- 数据可视化图表
- 批量文件处理
- 自定义转换规则
- 导出格式扩展

## 🤝 贡献指南

本项目为内部使用项目，如需修改或扩展功能，请：
1. 阅读相关技术文档
2. 遵循现有代码规范
3. 进行充分测试
4. 更新相关文档

## 📄 许可证

本项目仅供内部使用，未经授权不得外传或商用。

---

**版本**: v2.0
**最后更新**: 2024年
**维护团队**: 内部开发团队
