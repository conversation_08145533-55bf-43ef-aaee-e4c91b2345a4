/**
 * 应用常量配置
 * 集中管理所有常量和配置项
 */

// 文件处理相关常量
export const FILE_CONFIG = {
    // 支持的文件类型
    SUPPORTED_EXTENSIONS: ['.xlsx'],
    
    // 文件大小限制
    MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
    LARGE_FILE_THRESHOLD: 5 * 1024 * 1024, // 5MB
    
    // 分块读取配置
    CHUNK_SIZE: 1024 * 1024, // 1MB
    
    // 批处理配置
    BATCH_SIZE: 500,
    SMALL_BATCH_SIZE: 100,
    
    // 文件命名
    OUTPUT_FILE_PREFIX: '科陆流水线运维日志',
    OUTPUT_FILE_EXTENSION: '.xlsx'
};

// UI相关常量
export const UI_CONFIG = {
    // 消息显示时间
    SUCCESS_MESSAGE_DURATION: 3000,
    
    // 进度更新间隔
    PROGRESS_UPDATE_INTERVAL: 10,
    
    // 拖拽样式
    DRAG_OVER_BACKGROUND: '#e8f4fd',
    DRAG_LEAVE_BACKGROUND: '#f5f7fa',
    
    // 行高设置
    ROW_HEIGHT: 25, // 像素
    
    // 列宽设置
    COLUMN_WIDTHS: {
        '日期': 15,
        '维护保养情况': 25,
        '故障处理情况': 50,
        '备注': 15,
        'default': 20
    }
};

// Excel处理相关常量
export const EXCEL_CONFIG = {
    // 工作表名称
    WORKSHEET_NAME: '运维日志',
    
    // 主标题
    MAIN_TITLE: '科陆流水线日常运维及故障处理情况',
    
    // 默认值
    DEFAULT_REMARK: '已解决',
    
    // 需要移除的列
    EXCLUDED_COLUMNS: ['记录人'],
    
    // 新增列
    NEW_COLUMNS: {
        MAINTENANCE: '维护保养情况',
        REMARK: '备注'
    },
    
    // 日期格式
    DATE_FORMAT: 'yyyy-mm-dd',
    
    // 样式配置
    STYLES: {
        MAIN_TITLE: {
            font: {
                name: '宋体',
                size: 14,
                bold: true,
                color: { argb: 'FF000000' }
            },
            alignment: {
                horizontal: 'center',
                vertical: 'middle'
            }
        },
        COLUMN_HEADER: {
            font: {
                name: '宋体',
                size: 12,
                bold: true,
                color: { argb: 'FFFFFFFF' }
            },
            fill: {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: 'FF4472C4' }
            },
            alignment: {
                horizontal: 'center',
                vertical: 'middle'
            }
        },
        DATA_CELL: {
            font: {
                name: '宋体',
                size: 11,
                color: { argb: 'FF000000' }
            },
            alignment: {
                horizontal: 'center',
                vertical: 'middle',
                wrapText: true
            }
        },
        DATA_CELL_LEFT: {
            font: {
                name: '宋体',
                size: 11,
                color: { argb: 'FF000000' }
            },
            alignment: {
                horizontal: 'left',
                vertical: 'middle',
                wrapText: true
            }
        }
    }
};

// 日期处理相关常量
export const DATE_CONFIG = {
    // 日期关键词
    DATE_KEYWORDS: ['日期', 'date', '时间', 'time', '日', '月', '年'],
    
    // Excel日期系统
    EXCEL_DATE_SYSTEM: {
        BASE_DATE: new Date(1900, 0, 1),
        LEAP_YEAR_BUG_THRESHOLD: 60
    },
    
    // 日期格式
    DATE_GROUP_FORMAT: 'YYYY-MM-DD'
};

// 性能监控相关常量
export const PERFORMANCE_CONFIG = {
    // 异步处理配置
    async: {
        maxConcurrency: 3,
        batchSize: 1000,
        delayBetweenBatches: 10
    },
    
    // 内存管理配置
    memory: {
        maxMemoryUsage: 100 * 1024 * 1024, // 100MB
        gcThreshold: 80 * 1024 * 1024,     // 80MB
        cleanupInterval: 30000              // 30秒
    },
    
    // 监控指标
    metrics: {
        fileLoadTime: 'fileLoadTime',
        dataProcessTime: 'dataProcessTime',
        renderTime: 'renderTime'
    }
};

// 错误消息常量
export const ERROR_MESSAGES = {
    FILE_TYPE_ERROR: '请选择 .xlsx 格式的Excel文件',
    FILE_SIZE_ERROR: '文件大小不能超过10MB',
    FILE_READ_ERROR: '文件读取失败',
    FILE_EMPTY_ERROR: 'Excel文件为空或格式不正确',
    NO_WORKSHEET_ERROR: '工作簿中没有找到工作表',
    NO_DATA_ERROR: '工作表中没有数据',
    NO_HEADERS_ERROR: '无法读取表头信息',
    NO_DATE_COLUMN_ERROR: '未找到日期列，请确保Excel文件包含日期相关的列',
    WORKER_ERROR: 'Worker处理失败',
    DOWNLOAD_ERROR: '文件下载失败',
    PROCESSING_ERROR: '文件处理失败'
};

// 成功消息常量
export const SUCCESS_MESSAGES = {
    FILE_SELECTED: '文件选择成功，正在自动加载...',
    FILE_LOADED: '文件加载成功！请在下方月视图中选择需要导出的日期。',
    FILE_DOWNLOADED: '文件下载成功',
    DATA_PROCESSED: '数据处理完成'
};

// 信息消息常量
export const INFO_MESSAGES = {
    READING_FILE: '正在读取文件...',
    PROCESSING_DATA: '正在处理数据...',
    ANALYZING_DATES: '正在分析日期分布...',
    PARSING_EXCEL: '正在解析Excel文件...',
    LARGE_FILE_PROCESSING: '检测到大文件，使用后台处理...',
    GENERATING_OUTPUT: '正在生成输出文件...'
};

// DOM元素ID常量
export const DOM_IDS = {
    // 文件相关
    FILE_INPUT: 'fileInput',
    FILE_INFO: 'fileInfo',
    FILE_NAME: 'fileName',
    FILE_SIZE: 'fileSize',
    FILE_DATE: 'fileDate',
    CONVERT_BTN: 'convertBtn',
    
    // 消息和进度
    MESSAGE_AREA: 'messageArea',
    PROGRESS_CONTAINER: 'progressContainer',
    PROGRESS_BAR: 'progressBar',
    PROGRESS_TEXT: 'progressText',
    
    // 月视图相关
    MONTH_VIEW_SECTION: 'monthViewSection',
    TOTAL_RECORDS: 'totalRecords',
    DATE_RANGE: 'dateRange',
    SELECTED_COUNT: 'selectedCount',
    CURRENT_MONTH: 'currentMonth',
    CALENDAR_CONTAINER: 'calendarContainer',
    SELECTED_DATES_LIST: 'selectedDatesList',
    PREV_MONTH_BTN: 'prevMonth',
    NEXT_MONTH_BTN: 'nextMonth',
    CLEAR_SELECTION_BTN: 'clearSelection',
    PREVIEW_DATA_BTN: 'previewData',
    EXPORT_FILTERED_BTN: 'exportFiltered'
};

// CSS类名常量
export const CSS_CLASSES = {
    MESSAGE_SUCCESS: 'message success',
    MESSAGE_ERROR: 'message error',
    MESSAGE_INFO: 'message info',
    CALENDAR_DAY: 'calendar-day',
    CALENDAR_DAY_SELECTED: 'calendar-day selected',
    CALENDAR_DAY_HAS_DATA: 'calendar-day has-data',
    CALENDAR_DAY_OTHER_MONTH: 'calendar-day other-month'
};

// 导出所有配置的默认对象
export default {
    FILE_CONFIG,
    UI_CONFIG,
    EXCEL_CONFIG,
    DATE_CONFIG,
    PERFORMANCE_CONFIG,
    ERROR_MESSAGES,
    SUCCESS_MESSAGES,
    INFO_MESSAGES,
    DOM_IDS,
    CSS_CLASSES
};
